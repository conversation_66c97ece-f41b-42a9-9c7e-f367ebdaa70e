import os
import cv2
import glob
import torch
import numpy as np
import argparse
# 修改导入路径
from slr_network import SLRModel  # 根据项目结构调整导入
from utils.device import GpuDataParallel  # 修正导入路径
from collections import OrderedDict

parser = argparse.ArgumentParser()
parser.add_argument('--checkpoint', type=str, default='./work_dir/baseline_res18_attention/_best_model.pt')
parser.add_argument('--dataset', type=str, default='phoenix2014')
parser.add_argument('--prefix', type=str, default='./dataset/phoenix2014/phoenix-2014-multisigner')
parser.add_argument('--dict_path', type=str, default='./preprocess/phoenix2014/gloss_dict.npy')
parser.add_argument('--select_id', type=int, default=0)
parser.add_argument('--gpu_id', type=int, default=0)
args = parser.parse_args()

checkpoint = args.checkpoint
dataset = args.dataset
prefix = args.prefix
dict_path = args.dict_path
select_id = args.select_id
gpu_id = args.gpu_id

# 设置设备
device = GpuDataParallel()
device.set_device(gpu_id)

# 加载模型
model = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)  # 参数会在加载权重时更新
state_dict = torch.load(checkpoint)
if 'model_state_dict' in state_dict:
    state_dict = state_dict['model_state_dict']
state_dict = OrderedDict([(k.replace('.module', ''), v) for k, v in state_dict.items()])
model.load_state_dict(state_dict, strict=False)
model = model.to(device.output_device)
model.eval()

# 加载数据和应用转换
gloss_dict = np.load(dict_path, allow_pickle=True).item()
inputs_list = np.load(f"./preprocess/{dataset}/dev_info.npy", allow_pickle=True).item()
name = inputs_list[select_id]['fileid']
print(f'Generating CAM for {name}')
img_folder = os.path.join(prefix, "features/fullFrame-256x256px/" + inputs_list[select_id]['folder'])
img_list = sorted(glob.glob(img_folder + "/*.png"))
img_list = [cv2.cvtColor(cv2.imread(img_path), cv2.COLOR_BGR2RGB) for img_path in img_list]
label_list = []
for phase in inputs_list[select_id]['label'].split(" "):
    if phase == '':
        continue
    if phase in gloss_dict.keys():
        label_list.append(gloss_dict[phase][0])

# 预处理图像
from utils import video_augmentation  # 修正导入路径
transform = video_augmentation.Compose([
    video_augmentation.CenterCrop(224),
    video_augmentation.Resize(1.0),
    video_augmentation.ToTensor(),
])
vid, label = transform(img_list, label_list, None)
vid = vid.float() / 127.5 - 1
vid = vid.unsqueeze(0)

# 注册钩子以获取特征图
fmap_block = []

def forward_hook(module, input, output):
    fmap_block.append(output)  # N, C, T, H, W

# 根据模型结构注册钩子
model.conv2d.corr2.conv_back.register_forward_hook(forward_hook)

def cam_show_img(img, feature_map, grads, out_dir):  # img: ntchw, feature_map: ncthw, grads: ncthw
    N, C, T, H, W = feature_map.shape
    cam = np.zeros(feature_map.shape[2:], dtype=np.float32)  # thw
    grads = grads[0,:].reshape([C, T, -1])
    weights = np.mean(grads, axis=-1)
    for i in range(C):
        for j in range(T):
            cam[j] += weights[i,j] * feature_map[0, i, j, :, :]
    cam = np.maximum(cam, 0)

    if not os.path.exists(out_dir):
        os.makedirs(out_dir)
    else:
        import shutil
        shutil.rmtree(out_dir)
        os.makedirs(out_dir)
    
    for i in range(T):
        out_cam = cam[i]
        out_cam = out_cam - np.min(out_cam)
        out_cam = out_cam / (1e-7 + out_cam.max())
        out_cam = cv2.resize(out_cam, (img.shape[3], img.shape[4]))
        out_cam = (255 * out_cam).astype(np.uint8)
        heatmap = cv2.applyColorMap(out_cam, cv2.COLORMAP_JET)
        cam_img = np.float32(heatmap) / 255 + (img[0,i]/2+0.5).permute(1,2,0).cpu().data.numpy()
        cam_img = cam_img/np.max(cam_img)
        cam_img = np.uint8(255 * cam_img)
        path_cam_img = os.path.join(out_dir, f"cam_{i}.jpg")
        cv2.imwrite(path_cam_img, cam_img)
    print('Generated CAM images at', out_dir)

print("Video shape:", vid.shape)
vid = device.data_to_device(vid)
video_length = torch.LongTensor([vid.shape[1]])
vid_lgt = device.data_to_device(video_length)
label = device.data_to_device([torch.LongTensor(label)])
label_lgt = device.data_to_device(torch.LongTensor([len(label_list)]))

# 前向传播
with torch.no_grad():
    ret_dict = model(vid, vid_lgt, label=label, label_lgt=label_lgt)

# 加载预先计算的梯度值
grads_val = torch.load('./weight_map.pth').cpu().data.numpy()
fmap = fmap_block[0].cpu().data.numpy()

# 生成CAM图像
cam_show_img(vid, fmap, grads_val, out_dir='./CAM_images')
