import os
import cv2
import glob
import torch
import torch.nn.functional as F
import numpy as np
import argparse
import matplotlib.pyplot as plt
from collections import OrderedDict
import warnings
warnings.filterwarnings('ignore')

# 修改导入路径
from slr_network import SLRModel  # 根据项目结构调整导入
from utils.device import GpuDataParallel  # 修正导入路径


class GradCAM:
    """
    Grad-CAM implementation for Sign Language Recognition models with DynamicTemporalAttention
    """
    def __init__(self, model, target_layers=None):
        self.model = model
        # 更新默认目标层以匹配新的架构
        self.target_layers = target_layers or [
            'conv2d.corr2.fusion.0',  # DynamicTemporalAttention fusion layer
            'conv2d.corr1.fusion.0',  # Earlier attention layer
            'conv2d.layer4.1.conv2',  # ResNet layer
            'conv2d.layer3.1.conv2'   # ResNet layer
        ]
        self.gradients = {}
        self.activations = {}
        self.hooks = []

        # Register hooks
        self._register_hooks()

    def _register_hooks(self):
        """Register forward and backward hooks for target layers"""
        for layer_name in self.target_layers:
            layer = self._get_layer_by_name(layer_name)
            if layer is not None:
                # Forward hook to capture activations
                def make_forward_hook(name):
                    def hook(module, input, output):
                        self._save_activation(name, output)
                    return hook

                # Backward hook to capture gradients
                def make_backward_hook(name):
                    def hook(module, grad_input, grad_output):
                        if grad_output[0] is not None:
                            self._save_gradient(name, grad_output[0])
                    return hook

                forward_hook = layer.register_forward_hook(make_forward_hook(layer_name))
                backward_hook = layer.register_backward_hook(make_backward_hook(layer_name))
                self.hooks.extend([forward_hook, backward_hook])

    def _get_layer_by_name(self, layer_name):
        """Get layer by its name path"""
        layer = self.model
        for attr in layer_name.split('.'):
            if hasattr(layer, attr):
                layer = getattr(layer, attr)
            else:
                print(f"Warning: Layer {layer_name} not found")
                return None
        return layer

    def _save_activation(self, name, activation):
        """Save activation for later use"""
        self.activations[name] = activation.detach()

    def _save_gradient(self, name, gradient):
        """Save gradient for later use"""
        self.gradients[name] = gradient.detach()

    def generate_cam(self, input_tensor, vid_lgt=None, class_idx=None, layer_name=None):
        """
        Generate Grad-CAM for given input

        Args:
            input_tensor: Input video tensor (B, C, T, H, W)
            vid_lgt: Video length tensor
            class_idx: Target class index for CAM generation
            layer_name: Specific layer to generate CAM for

        Returns:
            cam: Generated CAM (T, H, W)
        """
        # Clear previous gradients and activations
        self.gradients.clear()
        self.activations.clear()

        # Forward pass
        self.model.eval()
        input_tensor.requires_grad_(True)

        # Prepare video length if not provided
        if vid_lgt is None:
            vid_lgt = torch.LongTensor([input_tensor.shape[2]]).to(input_tensor.device)

        # Get model output - note the correct dimension order for this model
        # The model expects (B, C, T, H, W) but processes it as (B, T, C, H, W) internally
        output = self.model(input_tensor, vid_lgt)

        # Use the sequence logits for CAM generation
        logits = output['sequence_logits']  # Shape: (T, B, num_classes)

        # If no class specified, use the predicted class
        if class_idx is None:
            # Get the most confident prediction across time
            mean_logits = logits.mean(0)  # Average over time: (B, num_classes)
            class_idx = torch.argmax(mean_logits, dim=1).item()

        # Compute loss for the target class
        target_score = logits[:, 0, class_idx].sum()  # Sum over time dimension

        # Backward pass
        self.model.zero_grad()
        target_score.backward(retain_graph=True)

        # Generate CAM for specified layer
        target_layer = layer_name or self.target_layers[0]

        if target_layer not in self.gradients or target_layer not in self.activations:
            print(f"Warning: No gradients or activations found for layer {target_layer}")
            return None

        gradients = self.gradients[target_layer]  # Shape depends on layer
        activations = self.activations[target_layer]  # Shape depends on layer

        # Handle different activation shapes
        if len(activations.shape) == 5:  # (B, C, T, H, W)
            # Compute weights by global average pooling of gradients
            weights = torch.mean(gradients, dim=(3, 4), keepdim=True)  # (B, C, T, 1, 1)

            # Generate CAM
            cam = torch.sum(weights * activations, dim=1)  # (B, T, H, W)
            cam = F.relu(cam)  # Apply ReLU

            # Normalize CAM
            cam = cam.squeeze(0)  # Remove batch dimension (T, H, W)

        elif len(activations.shape) == 4:  # (B, C, H, W) - single frame
            # Compute weights by global average pooling of gradients
            weights = torch.mean(gradients, dim=(2, 3), keepdim=True)  # (B, C, 1, 1)

            # Generate CAM
            cam = torch.sum(weights * activations, dim=1)  # (B, H, W)
            cam = F.relu(cam)  # Apply ReLU

            # Normalize CAM
            cam = cam.squeeze(0)  # Remove batch dimension (H, W)

            # Add temporal dimension for consistency
            cam = cam.unsqueeze(0)  # (1, H, W)

        else:
            print(f"Unsupported activation shape: {activations.shape}")
            return None

        return cam

    def remove_hooks(self):
        """Remove all registered hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()

parser = argparse.ArgumentParser(description='Generate Grad-CAM visualizations for SLR models')
parser.add_argument('--checkpoint', type=str, default='./work_dir/baseline_res18_attention/_best_model.pt',
                    help='Path to model checkpoint')
parser.add_argument('--dataset', type=str, default='phoenix2014',
                    help='Dataset name')
parser.add_argument('--prefix', type=str, default='./dataset/phoenix2014/phoenix-2014-multisigner',
                    help='Dataset prefix path')
parser.add_argument('--dict_path', type=str, default='./preprocess/phoenix2014/gloss_dict.npy',
                    help='Path to gloss dictionary')
parser.add_argument('--select_id', type=int, default=0,
                    help='Video ID to process')
parser.add_argument('--gpu_id', type=int, default=0,
                    help='GPU ID to use')
parser.add_argument('--output_dir', type=str, default='./CAM_images',
                    help='Output directory for CAM images')
parser.add_argument('--target_layers', type=str, nargs='+',
                    default=['conv2d.corr2.fusion.0', 'conv2d.corr1.fusion.0'],
                    help='Target layers for CAM generation (updated for DynamicTemporalAttention)')
parser.add_argument('--class_idx', type=int, default=None,
                    help='Target class index (if None, use predicted class)')
parser.add_argument('--save_original', action='store_true',
                    help='Save original frames alongside CAM')
parser.add_argument('--alpha', type=float, default=0.4,
                    help='Alpha blending factor for CAM overlay')
args = parser.parse_args()

def load_model_and_data(args):
    """Load model and prepare data"""
    # 设置设备
    device = GpuDataParallel()
    device.set_device(args.gpu_id)

    # 加载模型
    print(f"Loading model from {args.checkpoint}")
    model = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)

    state_dict = torch.load(args.checkpoint, map_location='cpu')
    if 'model_state_dict' in state_dict:
        state_dict = state_dict['model_state_dict']

    # Clean state dict keys
    state_dict = OrderedDict([(k.replace('.module', ''), v) for k, v in state_dict.items()])
    model.load_state_dict(state_dict, strict=False)
    model = model.to(device.output_device)
    model.eval()

    # 加载数据和应用转换
    print(f"Loading data for video ID {args.select_id}")
    gloss_dict = np.load(args.dict_path, allow_pickle=True).item()
    inputs_list = np.load(f"./preprocess/{args.dataset}/dev_info.npy", allow_pickle=True).item()

    if args.select_id >= len(inputs_list):
        raise ValueError(f"Video ID {args.select_id} out of range. Max ID: {len(inputs_list)-1}")

    video_info = inputs_list[args.select_id]
    name = video_info['fileid']
    print(f'Processing video: {name}')

    # Load images
    img_folder = os.path.join(args.prefix, "features/fullFrame-256x256px/" + video_info['folder'])
    img_list = sorted(glob.glob(img_folder + "/*.png"))

    if not img_list:
        raise FileNotFoundError(f"No images found in {img_folder}")

    img_list = [cv2.cvtColor(cv2.imread(img_path), cv2.COLOR_BGR2RGB) for img_path in img_list]

    # Process labels
    label_list = []
    for phase in video_info['label'].split(" "):
        if phase == '':
            continue
        if phase in gloss_dict.keys():
            label_list.append(gloss_dict[phase][0])

    return model, device, img_list, label_list, name


def preprocess_video(img_list, label_list, device):
    """Preprocess video data"""
    # 预处理图像
    from utils import video_augmentation
    transform = video_augmentation.Compose([
        video_augmentation.CenterCrop(224),
        video_augmentation.Resize(1.0),
        video_augmentation.ToTensor(),
    ])

    vid, label = transform(img_list, label_list, None)
    vid = vid.float() / 127.5 - 1
    vid = vid.unsqueeze(0)  # Add batch dimension

    print("Video shape:", vid.shape)
    vid = device.data_to_device(vid)
    video_length = torch.LongTensor([vid.shape[2]])  # Note: shape[2] is temporal dimension
    vid_lgt = device.data_to_device(video_length)
    label = device.data_to_device([torch.LongTensor(label)])
    label_lgt = device.data_to_device(torch.LongTensor([len(label_list)]))

    return vid, vid_lgt, label, label_lgt

def visualize_cam(cam, original_frames, output_dir, video_name, alpha=0.4, save_original=False):
    """
    Visualize CAM overlaid on original frames

    Args:
        cam: CAM tensor (T, H, W)
        original_frames: Original video frames
        output_dir: Output directory
        video_name: Video name for file naming
        alpha: Alpha blending factor
        save_original: Whether to save original frames
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Create subdirectory for this video
    video_dir = os.path.join(output_dir, video_name)
    if os.path.exists(video_dir):
        import shutil
        shutil.rmtree(video_dir)
    os.makedirs(video_dir)

    cam_np = cam.cpu().numpy() if torch.is_tensor(cam) else cam
    T, H, W = cam_np.shape

    for t in range(T):
        # Get original frame
        if t < len(original_frames):
            original_frame = original_frames[t]
            if original_frame.shape[:2] != (H, W):
                original_frame = cv2.resize(original_frame, (W, H))
        else:
            # Use last frame if video is shorter
            original_frame = cv2.resize(original_frames[-1], (W, H))

        # Normalize CAM
        cam_frame = cam_np[t]
        cam_frame = (cam_frame - cam_frame.min()) / (cam_frame.max() - cam_frame.min() + 1e-8)

        # Resize CAM to match frame size
        cam_resized = cv2.resize(cam_frame, (original_frame.shape[1], original_frame.shape[0]))

        # Apply colormap
        heatmap = cv2.applyColorMap((cam_resized * 255).astype(np.uint8), cv2.COLORMAP_JET)

        # Blend with original frame
        blended = cv2.addWeighted(original_frame, 1-alpha, heatmap, alpha, 0)

        # Save images
        frame_name = f"frame_{t:04d}"

        # Save CAM overlay
        cv2.imwrite(os.path.join(video_dir, f"{frame_name}_cam.jpg"),
                   cv2.cvtColor(blended, cv2.COLOR_RGB2BGR))

        # Save heatmap only
        cv2.imwrite(os.path.join(video_dir, f"{frame_name}_heatmap.jpg"), heatmap)

        # Save original frame if requested
        if save_original:
            cv2.imwrite(os.path.join(video_dir, f"{frame_name}_original.jpg"),
                       cv2.cvtColor(original_frame, cv2.COLOR_RGB2BGR))

    print(f'Generated CAM visualizations at {video_dir}')
    return video_dir


def main():
    """Main function"""
    try:
        # Load model and data
        model, device, img_list, label_list, video_name = load_model_and_data(args)

        # Preprocess video
        vid, vid_lgt, label, label_lgt = preprocess_video(img_list, label_list, device)

        # Initialize Grad-CAM
        grad_cam = GradCAM(model, target_layers=args.target_layers)

        print(f"Generating Grad-CAM for layers: {args.target_layers}")

        # Generate CAM for each target layer
        for layer_name in args.target_layers:
            print(f"Processing layer: {layer_name}")

            # Generate CAM
            cam = grad_cam.generate_cam(vid, vid_lgt=vid_lgt, class_idx=args.class_idx, layer_name=layer_name)

            if cam is not None:
                # Create layer-specific output directory
                layer_output_dir = os.path.join(args.output_dir, layer_name.replace('.', '_'))

                # Visualize CAM
                visualize_cam(cam, img_list, layer_output_dir, video_name,
                            alpha=args.alpha, save_original=args.save_original)
            else:
                print(f"Failed to generate CAM for layer {layer_name}")

        # Clean up hooks
        grad_cam.remove_hooks()

        print("Grad-CAM generation completed successfully!")

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
