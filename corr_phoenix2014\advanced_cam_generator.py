#!/usr/bin/env python3
"""
Advanced Grad-CAM generator with configuration file support
"""

import os
import yaml
import argparse
import logging
from pathlib import Path
import torch
import numpy as np
from tqdm import tqdm

# Import our modules
from generate_cam import GradCAM, load_model_and_data, preprocess_video, visualize_cam
from visualize_cam_comparison import create_layer_comparison, create_video_comparison


def setup_logging(config):
    """Setup logging configuration"""
    log_level = getattr(logging, config['logging']['level'])
    
    if config['logging']['save_logs']:
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(config['logging']['log_file']),
                logging.StreamHandler()
            ]
        )
    else:
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )


def load_config(config_path):
    """Load configuration from YAML file"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def create_args_from_config(config, video_id=0, experiment_name=None):
    """Create args object from configuration"""
    class Args:
        pass
    
    args = Args()
    
    # Model settings
    args.checkpoint = config['model']['checkpoint']
    
    # Dataset settings
    args.dataset = config['dataset']['name']
    args.prefix = config['dataset']['prefix']
    args.dict_path = config['dataset']['dict_path']
    
    # Processing settings
    args.select_id = video_id
    args.gpu_id = config['processing']['gpu_id']
    
    # CAM settings
    args.target_layers = config['cam']['target_layers']
    args.alpha = config['cam']['alpha']
    args.save_original = config['cam']['save_original']
    args.class_idx = None
    
    # Output settings
    if experiment_name:
        args.output_dir = os.path.join(config['cam']['output_dir'], experiment_name)
    else:
        args.output_dir = config['cam']['output_dir']
    
    return args


def run_experiment(config, experiment_name, layer_combination, video_ids=None):
    """Run a specific experiment with given layer combination"""
    logging.info(f"Running experiment: {experiment_name}")
    
    # Determine video IDs to process
    if video_ids is None:
        dev_info = np.load(config['dataset']['dev_info_path'], allow_pickle=True).item()
        total_videos = len(dev_info)
        
        start_id = config['batch']['start_id']
        end_id = config['batch']['end_id'] or total_videos - 1
        end_id = min(end_id, config['batch']['max_videos'] + start_id - 1)
        
        video_ids = list(range(start_id, end_id + 1))
    
    logging.info(f"Processing {len(video_ids)} videos")
    
    # Process each video
    failed_videos = []
    
    for video_id in tqdm(video_ids, desc=f"Experiment {experiment_name}"):
        try:
            # Create args for this video
            args = create_args_from_config(config, video_id, experiment_name)
            args.target_layers = layer_combination
            
            # Load model and data
            model, device, img_list, label_list, video_name = load_model_and_data(args)
            
            # Preprocess video
            vid, vid_lgt, label, label_lgt = preprocess_video(img_list, label_list, device)
            
            # Initialize Grad-CAM
            grad_cam = GradCAM(model, target_layers=args.target_layers)
            
            # Generate CAM for each target layer
            for layer_name in args.target_layers:
                try:
                    # Generate CAM
                    cam = grad_cam.generate_cam(vid, class_idx=args.class_idx, layer_name=layer_name)
                    
                    if cam is not None:
                        # Create layer-specific output directory
                        layer_output_dir = os.path.join(args.output_dir, f'video_{video_id:04d}', 
                                                      layer_name.replace('.', '_'))
                        
                        # Visualize CAM
                        visualize_cam(cam, img_list, layer_output_dir, video_name, 
                                    alpha=args.alpha, save_original=args.save_original)
                    else:
                        logging.warning(f"Failed to generate CAM for layer {layer_name} in video {video_id}")
                        
                except Exception as e:
                    logging.error(f"Error processing layer {layer_name} for video {video_id}: {e}")
            
            # Clean up hooks
            grad_cam.remove_hooks()
            
        except Exception as e:
            logging.error(f"Error processing video {video_id}: {e}")
            failed_videos.append(video_id)
            
            if not config['batch']['continue_on_error']:
                raise
    
    # Save failed videos list
    if failed_videos and config['batch']['save_failed_list']:
        failed_file = os.path.join(config['cam']['output_dir'], experiment_name, 'failed_videos.txt')
        os.makedirs(os.path.dirname(failed_file), exist_ok=True)
        with open(failed_file, 'w') as f:
            for vid_id in failed_videos:
                f.write(f"{vid_id}\n")
        logging.info(f"Failed video IDs saved to: {failed_file}")
    
    logging.info(f"Experiment {experiment_name} completed. Failed: {len(failed_videos)} videos")
    return failed_videos


def create_experiment_comparisons(config, experiment_name):
    """Create comparison visualizations for an experiment"""
    experiment_dir = os.path.join(config['cam']['output_dir'], experiment_name)
    comparison_dir = os.path.join(experiment_dir, 'comparisons')
    os.makedirs(comparison_dir, exist_ok=True)
    
    if config['cam']['create_layer_comparison']:
        logging.info("Creating layer comparison visualizations")
        
        # Find all video directories
        video_dirs = [d for d in Path(experiment_dir).iterdir() 
                     if d.is_dir() and d.name.startswith('video_')]
        
        for video_dir in video_dirs[:5]:  # Limit to first 5 videos for comparison
            video_name = video_dir.name
            output_path = os.path.join(comparison_dir, f"{video_name}_layer_comparison.png")
            
            try:
                create_layer_comparison(str(video_dir), output_path, 
                                      max_frames=config['visualization']['max_frames_per_comparison'])
            except Exception as e:
                logging.error(f"Error creating layer comparison for {video_name}: {e}")
    
    if config['cam']['create_video_summary']:
        logging.info("Creating video summary comparison")
        
        # Create video comparison for first layer
        video_dirs = [str(d) for d in Path(experiment_dir).iterdir() 
                     if d.is_dir() and d.name.startswith('video_')][:10]  # Limit to 10 videos
        
        if video_dirs:
            layer_name = config['cam']['target_layers'][0].replace('.', '_')
            output_path = os.path.join(comparison_dir, f"video_summary_{layer_name}.png")
            
            try:
                create_video_comparison(video_dirs, output_path, layer_name,
                                      max_frames=config['visualization']['max_frames_per_comparison'])
            except Exception as e:
                logging.error(f"Error creating video summary: {e}")


def main():
    parser = argparse.ArgumentParser(description='Advanced Grad-CAM generator with configuration support')
    parser.add_argument('--config', type=str, default='cam_config.yaml',
                        help='Path to configuration file')
    parser.add_argument('--experiment', type=str, default=None,
                        help='Specific experiment to run (from config)')
    parser.add_argument('--video_ids', type=int, nargs='+', default=None,
                        help='Specific video IDs to process')
    parser.add_argument('--create_comparisons', action='store_true',
                        help='Create comparison visualizations after processing')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Setup logging
    setup_logging(config)
    
    logging.info("Starting advanced CAM generation")
    
    # Create output directory
    os.makedirs(config['cam']['output_dir'], exist_ok=True)
    
    if args.experiment:
        # Run specific experiment
        if args.experiment in config['experiments']['layer_combinations']:
            layer_combination = config['experiments']['layer_combinations'][args.experiment]
            failed_videos = run_experiment(config, args.experiment, layer_combination, args.video_ids)
            
            if args.create_comparisons:
                create_experiment_comparisons(config, args.experiment)
        else:
            logging.error(f"Experiment '{args.experiment}' not found in configuration")
    else:
        # Run all experiments
        for exp_name, layer_combination in config['experiments']['layer_combinations'].items():
            failed_videos = run_experiment(config, exp_name, layer_combination, args.video_ids)
            
            if args.create_comparisons:
                create_experiment_comparisons(config, exp_name)
    
    logging.info("Advanced CAM generation completed!")


if __name__ == "__main__":
    main()
