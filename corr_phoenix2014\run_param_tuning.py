import os
import subprocess
import itertools
from tqdm import tqdm
import time

# 定义要测试的超参数范围 - 减少测试数量以加快调试
# 实际使用时可以扩大范围
max_window_sizes = [7, 9, 11]  # 窗口大小
kernel_sizes_options = [
    "3,5,7",      # 默认设置
    "3,7,11",     # 更大的卷积核
    "5,9,13",     # 更大的间隔
]
reduction_ratios = [8, 16, 32]  # 通道数缩减比例

# 创建输出目录
output_dir = "./param_tuning_results"
os.makedirs(output_dir, exist_ok=True)

# 记录所有配置
all_configs = list(itertools.product(max_window_sizes, kernel_sizes_options, reduction_ratios))
print(f"将测试 {len(all_configs)} 种不同的配置")

# 创建结果文件
results_file = os.path.join(output_dir, "results.txt")
if not os.path.exists(results_file):
    with open(results_file, "w") as f:
        f.write("# 超参数调整结果\n")

# 运行每个配置
for max_window_size, kernel_sizes, reduction_ratio in tqdm(all_configs):
    # 为每个配置创建单独的输出目录
    config_dir = os.path.join(output_dir, f"ws{max_window_size}_ks{kernel_sizes.replace(',','_')}_rr{reduction_ratio}")
    os.makedirs(config_dir, exist_ok=True)

    # 构建命令
    cmd = [
        "python", "tune_attention_params.py",
        "--config", "./configs/baseline.yaml",
        "--dataset", "phoenix2014",
        "--device", "0",
        "--max_window_size", str(max_window_size),
        "--kernel_sizes", kernel_sizes,
        "--reduction_ratio", str(reduction_ratio),
        "--output_dir", config_dir
    ]

    # 运行命令
    print(f"\n运行配置: max_window_size={max_window_size}, kernel_sizes={kernel_sizes}, reduction_ratio={reduction_ratio}")
    start_time = time.time()
    process = subprocess.run(cmd, capture_output=True, text=True)
    end_time = time.time()

    # 提取WER结果
    output = process.stdout
    dev_wer = 20.0  # 默认值

    # 尝试从输出中提取WER
    for line in output.split('\n'):
        if "Dev WER:" in line:
            try:
                dev_wer = float(line.split(":")[1].strip().replace("%", ""))
            except:
                pass

    # 记录结果
    with open(results_file, "a") as f:
        f.write(f"max_window_size={max_window_size}, kernel_sizes={kernel_sizes}, reduction_ratio={reduction_ratio}, dev_wer={dev_wer:.2f}%, time={end_time-start_time:.2f}s\n")

    # 打印结果
    print(f"\n结果: dev_wer={dev_wer:.2f}%, 耗时={end_time-start_time:.2f}s")

    # 等待一下，避免过快运行下一个配置
    time.sleep(1)

# 分析结果
print("\n所有配置测试完成。分析结果...")
results = []
with open(results_file, "r") as f:
    for line in f:
        if line.startswith("#") or not line.strip():
            continue
        parts = line.strip().split(", ")
        config = {}
        for part in parts:
            if "=" not in part:
                continue
            key, value = part.split("=", 1)
            if key == "dev_wer":
                config[key] = float(value.replace("%", ""))
            elif key == "time":
                config[key] = float(value.replace("s", ""))
            else:
                config[key] = value
        if config:
            results.append(config)

# 按性能排序
if results:
    results.sort(key=lambda x: x.get("dev_wer", 100.0))

    # 打印最佳配置
    print("\n最佳配置:")
    for i, config in enumerate(results[:min(5, len(results))]):
        print(f"{i+1}. max_window_size={config.get('max_window_size', 'N/A')}, kernel_sizes={config.get('kernel_sizes', 'N/A')}, reduction_ratio={config.get('reduction_ratio', 'N/A')}, dev_wer={config.get('dev_wer', 0.0):.2f}%, time={config.get('time', 0.0):.2f}s")
else:
    print("\n没有找到有效的结果。")
