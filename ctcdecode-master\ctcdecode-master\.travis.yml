dist: bionic
language: python
python:
  - '3.5'
  - '3.6'
  - '3.7'
  - '3.8'
install:
  - pip install -r requirements.txt
  - pip install .
script:
  - pytest tests/

jobs:
  include:
    - stage: deploy
      python: 3.8
      install:
        - pip install -r requirements.txt
        - pip install .
      script:
        - pytest tests/
      deploy:
        provider: pypi
        username: __token__
        password:
          secure: vUbqVM3nP1587JuwCcp4MPutC5qaSQ6tJcMw9d9oahQM/S/tMXOOcZEGAhc8Toh2FesrupxJ4uInJhK1++ydQw832MaUnNG9OsjdSM8fdMTsziLOvIgifFe8vsS2NsGJTE9ty5JIiJYFXBrx187YECu1AttyrzB4Y/QsIIZuUmMHZtRE0tJt4ZPp8KWY91wuq5TcgyXjbRYODlcHscMYoJqZBOIwXcb/rRFciIIrm9hhwJvVfZ1tI3wZ5Lnl3rj9dmBangc+wHa1gFI88A+kewgM3e76Z+07S0va8pXte6oD/X9SYKCPsisctEE0nIqV4pvr7sYyeS1yerNBC+LlmbR0xfU9nA/WcXQszX2Chz9Cz/3jIVV/Z7Jx8ddg78nqvIxb38AduUIB8t68CRpx6MR66QujLyNnARQjOuVA8thOFOmw8fVxBVhIcOpW7AAb9pZKW/m15IqvOgTBLp6x2lPPUi6eSYLR/C/qbPGNNiI3tgxXRBSs/kK+/dMun4pMFJ2oNmMvz1ZgSnMKJnkzjBN7PmzkZuurrgSuzmq61mrCyeaLHDJ/xjj00pV7Nn4mNbywzjNeiouYtgZDontZ/e2m5NU/fsr1YKNbZ0PtQT+ZT7qdiA2R3Hf3hLEVl+kADipvx0VTBg5ZjBOESpIXpa/JMj/JHApMCzQD6bZcU78=
        on:
          tags: true
        skip_cleanup: true