"""
可视化动态时空注意力与其他方法的对比
"""
import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，适合服务器环境

# 配置参数
output_dir = './paper_figures/method_comparison'  # 输出目录

# 创建输出目录
os.makedirs(output_dir, exist_ok=True)

# 示例数据 - 在实际使用时，您需要替换为真实的实验结果
def get_comparison_data():
    """获取方法对比数据"""
    # 不同方法在Phoenix-2014上的WER (%)
    phoenix_data = {
        'Method': ['Baseline', 'Conv-Seq2Seq', 'Transformer', 'SAT', 'Ours (DTA)'],
        'Dev': [23.1, 21.4, 20.7, 19.2, 18.9],
        'Test': [24.5, 22.9, 21.5, 20.3, 19.8]
    }
    
    # 不同方法在CSL数据集上的WER (%)
    csl_data = {
        'Method': ['Baseline', 'Conv-Seq2Seq', 'Transformer', 'SAT', 'Ours (DTA)'],
        'Dev': [31.2, 29.5, 28.1, 27.3, 26.5],
        'Test': [32.8, 30.7, 29.4, 28.6, 27.9]
    }
    
    # 不同方法的计算复杂度（相对值）
    complexity_data = {
        'Method': ['Baseline', 'Conv-Seq2Seq', 'Transformer', 'SAT', 'Ours (DTA)'],
        'Parameters (M)': [11.2, 13.5, 15.8, 14.7, 12.3],
        'FLOPs (G)': [2.1, 2.8, 3.5, 3.2, 2.4],
        'Inference Time (ms)': [15, 22, 28, 25, 18]
    }
    
    # 不同方法的消融实验结果
    ablation_data = {
        'Method': ['Full Model', 'w/o Dynamic Window', 'w/o Multi-scale', 'w/o Temporal Gate'],
        'WER (%)': [18.9, 20.3, 19.7, 21.2]
    }
    
    return phoenix_data, csl_data, complexity_data, ablation_data

def visualize_performance_comparison(phoenix_data, csl_data):
    """可视化不同方法的性能对比"""
    # Phoenix-2014 数据集结果
    phoenix_df = pd.DataFrame(phoenix_data)
    
    # 创建分组条形图
    plt.figure(figsize=(12, 6))
    
    # 设置条形图位置
    x = np.arange(len(phoenix_df['Method']))
    width = 0.35
    
    # 绘制Dev和Test结果
    plt.bar(x - width/2, phoenix_df['Dev'], width, label='Dev', color='skyblue')
    plt.bar(x + width/2, phoenix_df['Test'], width, label='Test', color='lightcoral')
    
    # 添加标签和图例
    plt.xlabel('方法', fontsize=12)
    plt.ylabel('词错误率 (WER %)', fontsize=12)
    plt.title('Phoenix-2014数据集上不同方法的性能对比', fontsize=14)
    plt.xticks(x, phoenix_df['Method'], rotation=45, ha='right')
    plt.legend()
    
    # 添加数值标签
    for i, v in enumerate(phoenix_df['Dev']):
        plt.text(i - width/2, v + 0.3, f'{v:.1f}', ha='center')
    for i, v in enumerate(phoenix_df['Test']):
        plt.text(i + width/2, v + 0.3, f'{v:.1f}', ha='center')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'phoenix_performance.png'), dpi=300)
    plt.close()
    
    # CSL 数据集结果
    csl_df = pd.DataFrame(csl_data)
    
    # 创建分组条形图
    plt.figure(figsize=(12, 6))
    
    # 设置条形图位置
    x = np.arange(len(csl_df['Method']))
    width = 0.35
    
    # 绘制Dev和Test结果
    plt.bar(x - width/2, csl_df['Dev'], width, label='Dev', color='skyblue')
    plt.bar(x + width/2, csl_df['Test'], width, label='Test', color='lightcoral')
    
    # 添加标签和图例
    plt.xlabel('方法', fontsize=12)
    plt.ylabel('词错误率 (WER %)', fontsize=12)
    plt.title('CSL数据集上不同方法的性能对比', fontsize=14)
    plt.xticks(x, csl_df['Method'], rotation=45, ha='right')
    plt.legend()
    
    # 添加数值标签
    for i, v in enumerate(csl_df['Dev']):
        plt.text(i - width/2, v + 0.3, f'{v:.1f}', ha='center')
    for i, v in enumerate(csl_df['Test']):
        plt.text(i + width/2, v + 0.3, f'{v:.1f}', ha='center')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'csl_performance.png'), dpi=300)
    plt.close()

def visualize_complexity_comparison(complexity_data):
    """可视化不同方法的计算复杂度对比"""
    complexity_df = pd.DataFrame(complexity_data)
    
    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 参数量对比
    sns.barplot(x='Method', y='Parameters (M)', data=complexity_df, ax=axes[0], palette='Blues_d')
    axes[0].set_title('参数量对比 (百万)', fontsize=12)
    axes[0].set_xticklabels(axes[0].get_xticklabels(), rotation=45, ha='right')
    
    # FLOPs对比
    sns.barplot(x='Method', y='FLOPs (G)', data=complexity_df, ax=axes[1], palette='Greens_d')
    axes[1].set_title('计算量对比 (GFLOPs)', fontsize=12)
    axes[1].set_xticklabels(axes[1].get_xticklabels(), rotation=45, ha='right')
    
    # 推理时间对比
    sns.barplot(x='Method', y='Inference Time (ms)', data=complexity_df, ax=axes[2], palette='Reds_d')
    axes[2].set_title('推理时间对比 (毫秒)', fontsize=12)
    axes[2].set_xticklabels(axes[2].get_xticklabels(), rotation=45, ha='right')
    
    # 添加数值标签
    for ax, col in zip(axes, ['Parameters (M)', 'FLOPs (G)', 'Inference Time (ms)']):
        for i, v in enumerate(complexity_df[col]):
            ax.text(i, v + 0.1, f'{v:.1f}', ha='center')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'complexity_comparison.png'), dpi=300)
    plt.close()

def visualize_ablation_study(ablation_data):
    """可视化消融实验结果"""
    ablation_df = pd.DataFrame(ablation_data)
    
    plt.figure(figsize=(10, 6))
    
    # 绘制条形图
    bars = plt.bar(ablation_df['Method'], ablation_df['WER (%)'], color=sns.color_palette('muted'))
    
    # 突出显示完整模型
    bars[0].set_color('darkred')
    
    # 添加标签
    plt.xlabel('模型变体', fontsize=12)
    plt.ylabel('词错误率 (WER %)', fontsize=12)
    plt.title('动态时空注意力的消融实验', fontsize=14)
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 添加数值标签
    for i, v in enumerate(ablation_df['WER (%)']):
        plt.text(i, v + 0.2, f'{v:.1f}', ha='center')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'ablation_study.png'), dpi=300)
    plt.close()

def visualize_radar_chart():
    """可视化雷达图对比不同方法的各项指标"""
    # 定义各个方法在不同指标上的得分（满分5分）
    methods = ['Baseline', 'Conv-Seq2Seq', 'Transformer', 'SAT', 'Ours (DTA)']
    metrics = ['准确率', '速度', '参数量', '鲁棒性', '可解释性']
    
    # 各方法在各指标上的得分（示例数据）
    scores = np.array([
        [3.0, 4.5, 4.5, 3.0, 2.5],  # Baseline
        [3.5, 3.5, 3.5, 3.5, 3.0],  # Conv-Seq2Seq
        [4.0, 3.0, 3.0, 4.0, 3.5],  # Transformer
        [4.2, 3.2, 3.3, 4.2, 4.0],  # SAT
        [4.5, 4.0, 4.0, 4.3, 4.5],  # Ours (DTA)
    ])
    
    # 计算角度
    angles = np.linspace(0, 2*np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # 闭合雷达图
    
    # 准备数据
    metrics += metrics[:1]  # 闭合标签
    
    # 创建雷达图
    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(polar=True))
    
    # 设置角度标签
    ax.set_theta_offset(np.pi / 2)
    ax.set_theta_direction(-1)
    ax.set_thetagrids(np.degrees(angles[:-1]), metrics[:-1])
    
    # 绘制每个方法的雷达图
    colors = ['blue', 'green', 'orange', 'purple', 'red']
    for i, method in enumerate(methods):
        values = scores[i].tolist()
        values += values[:1]  # 闭合数据
        ax.plot(angles, values, color=colors[i], linewidth=2, label=method)
        ax.fill(angles, values, color=colors[i], alpha=0.25)
    
    # 设置雷达图范围
    ax.set_ylim(0, 5)
    
    # 添加图例
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    plt.title('不同方法的综合性能对比', fontsize=15, pad=20)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'radar_comparison.png'), dpi=300)
    plt.close()

def main():
    """主函数"""
    # 获取对比数据
    phoenix_data, csl_data, complexity_data, ablation_data = get_comparison_data()
    
    # 生成各种可视化
    visualize_performance_comparison(phoenix_data, csl_data)
    visualize_complexity_comparison(complexity_data)
    visualize_ablation_study(ablation_data)
    visualize_radar_chart()
    
    print(f"所有方法对比可视化已保存到 {output_dir}")

if __name__ == "__main__":
    main()
