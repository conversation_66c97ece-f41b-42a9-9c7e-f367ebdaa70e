#!/usr/bin/env python3
"""
Test script for DynamicTemporalAttention CAM generation
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

def test_dta_imports():
    """Test DynamicTemporalAttention specific imports"""
    print("Testing DynamicTemporalAttention imports...")
    
    try:
        from modules.attention import DynamicTemporalAttention
        print("✓ DynamicTemporalAttention")
    except ImportError as e:
        print(f"✗ DynamicTemporalAttention import failed: {e}")
        return False
    
    try:
        from generate_cam_dta import DynamicTemporalAttentionCAM
        print("✓ DynamicTemporalAttentionCAM")
    except ImportError as e:
        print(f"✗ DynamicTemporalAttentionCAM import failed: {e}")
        return False
    
    try:
        from slr_network import SLRModel
        print("✓ SLRModel")
    except ImportError as e:
        print(f"✗ SLRModel import failed: {e}")
        return False
    
    return True


def test_dta_model_creation():
    """Test DynamicTemporalAttention model creation"""
    print("\nTesting DTA model creation...")
    
    try:
        from slr_network import SLRModel
        
        # Test with default attention parameters
        model = SLRModel(
            num_classes=1000, 
            c2d_type='resnet18', 
            conv_type=2, 
            use_bn=1
        )
        print("✓ Model created with default attention parameters")
        
        # Test with custom attention parameters
        attention_params = {
            'max_window_size': 9,
            'kernel_sizes': [3, 5, 7],
            'reduction_ratio': 16
        }
        
        model_custom = SLRModel(
            num_classes=1000, 
            c2d_type='resnet18', 
            conv_type=2, 
            use_bn=1,
            attention_params=attention_params
        )
        print("✓ Model created with custom attention parameters")
        
        # Check if DTA modules exist
        if hasattr(model.conv2d, 'corr1') and hasattr(model.conv2d, 'corr2'):
            print("✓ DynamicTemporalAttention modules found (corr1, corr2)")
        else:
            print("✗ DynamicTemporalAttention modules not found")
            return False
        
        # Check DTA module structure
        dta_module = model.conv2d.corr1
        required_attrs = ['fusion', 'temporal_gate', 'window_convs']
        
        for attr in required_attrs:
            if hasattr(dta_module, attr):
                print(f"✓ DTA module has {attr}")
            else:
                print(f"⚠ DTA module missing {attr} (might be optional)")
        
        return True
        
    except Exception as e:
        print(f"✗ DTA model creation failed: {e}")
        return False


def test_dta_target_layers():
    """Test if DTA target layers exist"""
    print("\nTesting DTA target layers...")
    
    try:
        from slr_network import SLRModel
        
        model = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)
        
        # Test target layers for DTA
        target_layers = [
            'conv2d.corr2.fusion.0',
            'conv2d.corr2.temporal_gate.0',
            'conv2d.corr1.fusion.0',
            'conv2d.corr1.temporal_gate.0',
            'conv2d.layer4.1.conv2',
            'conv2d.layer3.1.conv2'
        ]
        
        found_layers = []
        missing_layers = []
        
        for layer_name in target_layers:
            layer = model
            try:
                for attr in layer_name.split('.'):
                    layer = getattr(layer, attr)
                print(f"✓ Layer {layer_name} found")
                found_layers.append(layer_name)
            except AttributeError:
                print(f"✗ Layer {layer_name} not found")
                missing_layers.append(layer_name)
        
        if found_layers:
            print(f"✓ Found {len(found_layers)} target layers")
            return True
        else:
            print("✗ No target layers found")
            return False
        
    except Exception as e:
        print(f"✗ Target layer test failed: {e}")
        return False


def test_dta_cam_initialization():
    """Test DynamicTemporalAttentionCAM initialization"""
    print("\nTesting DTA CAM initialization...")
    
    try:
        from slr_network import SLRModel
        from generate_cam_dta import DynamicTemporalAttentionCAM
        
        model = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)
        
        # Test with default layers
        dta_cam = DynamicTemporalAttentionCAM(model)
        print("✓ DTA CAM initialized with default layers")
        print(f"✓ Target layers: {dta_cam.target_layers}")
        print(f"✓ Hooks registered: {len(dta_cam.hooks)}")
        
        # Test with custom layers
        custom_layers = ['conv2d.corr2.fusion.0', 'conv2d.layer4.1.conv2']
        dta_cam_custom = DynamicTemporalAttentionCAM(model, target_layers=custom_layers)
        print("✓ DTA CAM initialized with custom layers")
        
        # Clean up
        dta_cam.remove_hooks()
        dta_cam_custom.remove_hooks()
        print("✓ Hooks removed successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ DTA CAM initialization failed: {e}")
        return False


def test_dta_forward_pass():
    """Test DTA model forward pass"""
    print("\nTesting DTA forward pass...")
    
    try:
        from slr_network import SLRModel
        
        model = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)
        model.eval()
        
        # Create dummy input (B, C, T, H, W)
        batch_size = 1
        channels = 3
        temporal = 16
        height = 224
        width = 224
        
        dummy_input = torch.randn(batch_size, channels, temporal, height, width)
        vid_lgt = torch.LongTensor([temporal])
        
        print(f"✓ Created dummy input: {dummy_input.shape}")
        
        # Forward pass
        with torch.no_grad():
            output = model(dummy_input, vid_lgt)
        
        print("✓ Forward pass successful")
        print(f"✓ Output keys: {list(output.keys())}")
        
        if 'sequence_logits' in output:
            seq_logits = output['sequence_logits']
            print(f"✓ Sequence logits shape: {seq_logits.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ DTA forward pass failed: {e}")
        return False


def test_attention_config():
    """Test attention configuration loading"""
    print("\nTesting attention configuration...")
    
    try:
        import yaml
        
        # Test config loading
        config_path = 'cam_config.yaml'
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            if 'attention_params' in config:
                attention_params = config['attention_params']
                print("✓ Attention parameters found in config:")
                for key, value in attention_params.items():
                    print(f"  {key}: {value}")
            else:
                print("⚠ No attention_params in config file")
            
            if 'cam' in config and 'target_layers' in config['cam']:
                target_layers = config['cam']['target_layers']
                print(f"✓ Target layers in config: {len(target_layers)} layers")
                for layer in target_layers:
                    print(f"  - {layer}")
            
            return True
        else:
            print(f"⚠ Config file {config_path} not found")
            return False
        
    except Exception as e:
        print(f"✗ Attention config test failed: {e}")
        return False


def create_test_script():
    """Create a simple test script for DTA CAM"""
    print("\nCreating test script...")
    
    test_script = '''#!/usr/bin/env python3
"""
Simple test for DTA CAM generation
"""

import torch
from slr_network import SLRModel
from generate_cam_dta import DynamicTemporalAttentionCAM

# Create model
model = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)
model.eval()

# Create dummy input
dummy_input = torch.randn(1, 3, 16, 224, 224)  # (B, C, T, H, W)
vid_lgt = torch.LongTensor([16])

# Initialize DTA CAM
dta_cam = DynamicTemporalAttentionCAM(model, target_layers=['conv2d.corr2.fusion.0'])

# Generate CAM
cam, attention_info = dta_cam.generate_cam(dummy_input, vid_lgt=vid_lgt)

if cam is not None:
    print(f"✓ CAM generated successfully! Shape: {cam.shape}")
    print(f"✓ Attention info keys: {list(attention_info.keys()) if attention_info else 'None'}")
else:
    print("✗ CAM generation failed")

# Clean up
dta_cam.remove_hooks()
print("✓ Test completed")
'''
    
    try:
        with open('test_dta_simple.py', 'w') as f:
            f.write(test_script)
        print("✓ Test script created: test_dta_simple.py")
        return True
    except Exception as e:
        print(f"✗ Failed to create test script: {e}")
        return False


def main():
    """Run all DTA tests"""
    print("=== DynamicTemporalAttention CAM Test ===\n")
    
    tests = [
        ("DTA Import Test", test_dta_imports),
        ("DTA Model Creation Test", test_dta_model_creation),
        ("DTA Target Layers Test", test_dta_target_layers),
        ("DTA CAM Initialization Test", test_dta_cam_initialization),
        ("DTA Forward Pass Test", test_dta_forward_pass),
        ("Attention Config Test", test_attention_config),
        ("Create Test Script", create_test_script)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("DTA TEST SUMMARY")
    print('='*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status:<8} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All DTA tests passed! Your DynamicTemporalAttention CAM setup is ready.")
        print("\nNext steps:")
        print("1. Run: python test_dta_simple.py")
        print("2. Generate CAM: python generate_cam_dta.py --help")
        print("3. Batch processing: python batch_generate_cam_dta.py --help")
    else:
        print(f"\n⚠ {total-passed} tests failed. Please fix the issues above.")
        
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
