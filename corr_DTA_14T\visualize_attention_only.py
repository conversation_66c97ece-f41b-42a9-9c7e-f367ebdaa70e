"""
简化版的动态时空注意力可视化脚本
专注于生成注意力机制的可视化，不依赖于CAM实现
"""
import os
import numpy as np
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from collections import OrderedDict
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，适合服务器环境

# 导入必要的模块
from slr_network import SLRModel
from modules.attention import DynamicTemporalAttention
import utils

# 配置参数
gpu_id = 0  # 使用的GPU ID
dataset = 'phoenix2014-T'  # 支持 [phoenix2014, phoenix2014-T, CSL-Daily]
model_weights = './pretrain_model/dev_18.90_PHOENIX14-T.pt'  # 替换为您的模型路径
output_dir = './attention_visualization'  # 输出目录

# 创建输出目录
os.makedirs(output_dir, exist_ok=True)

def generate_attention_mechanism_figure():
    """生成动态时空注意力机制的示意图"""
    plt.figure(figsize=(12, 8))
    
    # 创建一个3x3的网格
    gs = plt.GridSpec(3, 3, figure=plt.gcf())
    
    # 1. 输入特征表示
    ax1 = plt.subplot(gs[0, 0])
    ax1.imshow(np.random.rand(10, 20), cmap='viridis')
    ax1.set_title('Input Features')
    ax1.set_xticks([])
    ax1.set_yticks([])
    
    # 2. 多尺度卷积核
    ax2 = plt.subplot(gs[0, 1])
    kernel_sizes = [5, 9, 13]  # 与预训练模型匹配
    for i, k in enumerate(kernel_sizes):
        rect = plt.Rectangle((i*3, 0), 2, k, fc='skyblue', ec='blue', alpha=0.7)
        ax2.add_patch(rect)
    ax2.set_xlim(0, 10)
    ax2.set_ylim(0, 15)  # 增大y轴范围以适应更大的卷积核
    ax2.set_title('Multi-scale Kernels')
    ax2.set_xticks([])
    ax2.set_yticks([])
    
    # 3. 动态权重预测
    ax3 = plt.subplot(gs[0, 2])
    t = np.linspace(0, 10, 100)
    for i, k in enumerate(kernel_sizes):
        ax3.plot(t, 0.3 + 0.1 * np.sin(t + i), label=f'Kernel {k}')
    ax3.set_title('Dynamic Weight Prediction')
    ax3.legend(fontsize='small')
    
    # 4. 时间门控机制
    ax4 = plt.subplot(gs[1, 0:2])
    t = np.linspace(0, 10, 100)
    gate = 0.5 + 0.4 * np.sin(t)
    ax4.plot(t, gate)
    ax4.set_title('Temporal Gating Mechanism')
    ax4.set_xlabel('Time Steps')
    ax4.set_ylabel('Gate Value')
    
    # 5. 特征融合
    ax5 = plt.subplot(gs[1, 2])
    ax5.imshow(np.random.rand(10, 20), cmap='plasma')
    ax5.set_title('Feature Fusion')
    ax5.set_xticks([])
    ax5.set_yticks([])
    
    # 6. 整体流程图
    ax6 = plt.subplot(gs[2, :])
    # 创建一个简单的流程图
    nodes = ['Input', 'Multi-scale Conv', 'Dynamic Weights', 'Feature Fusion', 'Temporal Gate', 'Output']
    node_positions = np.array([[0, 0], [1, 0], [2, 1], [3, 0], [4, 1], [5, 0]])
    
    # 绘制节点
    for i, (node, pos) in enumerate(zip(nodes, node_positions)):
        circle = plt.Circle((pos[0], pos[1]), 0.2, fc='lightblue', ec='blue')
        ax6.add_patch(circle)
        ax6.text(pos[0], pos[1], node, ha='center', va='center', fontsize=9)
    
    # 绘制连接线
    arrows = [(0, 1), (1, 3), (2, 3), (3, 5), (4, 5)]
    for start, end in arrows:
        start_pos = node_positions[start]
        end_pos = node_positions[end]
        ax6.arrow(start_pos[0] + 0.2, start_pos[1], 
                 end_pos[0] - start_pos[0] - 0.4, end_pos[1] - start_pos[1],
                 head_width=0.05, head_length=0.1, fc='black', ec='black')
    
    ax6.set_xlim(-0.5, 5.5)
    ax6.set_ylim(-0.5, 1.5)
    ax6.set_title('Dynamic Spatiotemporal Attention Flow')
    ax6.set_xticks([])
    ax6.set_yticks([])
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'attention_mechanism.png'), dpi=300)
    plt.close()

def generate_window_weights_visualization():
    """生成窗口权重可视化"""
    plt.figure(figsize=(10, 6))
    
    # 模拟不同时间步的窗口权重
    t = np.linspace(0, 20, 100)  # 时间步
    kernel_sizes = [5, 9, 13]  # 卷积核大小，与预训练模型匹配
    
    # 生成模拟的窗口权重
    for i, k in enumerate(kernel_sizes):
        # 创建一个随时间变化的权重曲线
        weights = 0.2 + 0.3 * np.sin(t/5 + i) + 0.1 * np.cos(t/2)
        # 确保权重在合理范围内
        weights = np.clip(weights, 0, 1)
        plt.plot(t, weights, linewidth=2, label=f'Kernel Size {k}')
    
    plt.title('Dynamic Window Weights Over Time', fontsize=14)
    plt.xlabel('Time Steps', fontsize=12)
    plt.ylabel('Weight Value', fontsize=12)
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'window_weights.png'), dpi=300)
    plt.close()

def generate_temporal_gate_visualization():
    """生成时间门控可视化"""
    # 创建一个2x2的图表网格
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. 时间门控值随时间变化
    t = np.linspace(0, 20, 100)
    gate_values = 0.4 + 0.4 * np.sin(t/3) + 0.1 * np.cos(t)
    gate_values = np.clip(gate_values, 0, 1)
    
    axes[0, 0].plot(t, gate_values, linewidth=2, color='darkred')
    axes[0, 0].set_title('Temporal Gate Values', fontsize=12)
    axes[0, 0].set_xlabel('Time Steps', fontsize=10)
    axes[0, 0].set_ylabel('Gate Value', fontsize=10)
    axes[0, 0].grid(True, linestyle='--', alpha=0.7)
    
    # 2. 门控热力图 - 时间步 t=5
    gate_map_t5 = np.zeros((20, 20))
    # 创建一个中心高，边缘低的高斯分布
    x, y = np.meshgrid(np.linspace(-2, 2, 20), np.linspace(-2, 2, 20))
    d = np.sqrt(x*x + y*y)
    sigma, mu = 1.0, 0.0
    gate_map_t5 = np.exp(-((d-mu)**2 / (2.0 * sigma**2)))
    
    im = axes[0, 1].imshow(gate_map_t5, cmap='viridis')
    axes[0, 1].set_title('Gate Heatmap - Time Step 5', fontsize=12)
    axes[0, 1].set_xticks([])
    axes[0, 1].set_yticks([])
    fig.colorbar(im, ax=axes[0, 1])
    
    # 3. 门控热力图 - 时间步 t=10
    gate_map_t10 = np.zeros((20, 20))
    # 创建一个右上角高，其他区域低的分布
    for i in range(20):
        for j in range(20):
            gate_map_t10[i, j] = 0.8 * np.exp(-((i-5)**2 + (j-15)**2) / 50)
    
    im = axes[1, 0].imshow(gate_map_t10, cmap='viridis')
    axes[1, 0].set_title('Gate Heatmap - Time Step 10', fontsize=12)
    axes[1, 0].set_xticks([])
    axes[1, 0].set_yticks([])
    fig.colorbar(im, ax=axes[1, 0])
    
    # 4. 门控热力图 - 时间步 t=15
    gate_map_t15 = np.zeros((20, 20))
    # 创建一个水平条纹状的分布
    for i in range(20):
        for j in range(20):
            gate_map_t15[i, j] = 0.7 * np.sin(i/2) + 0.3
    
    im = axes[1, 1].imshow(gate_map_t15, cmap='viridis')
    axes[1, 1].set_title('Gate Heatmap - Time Step 15', fontsize=12)
    axes[1, 1].set_xticks([])
    axes[1, 1].set_yticks([])
    fig.colorbar(im, ax=axes[1, 1])
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'temporal_gate.png'), dpi=300)
    plt.close()

def generate_feature_comparison():
    """生成注意力前后的特征对比"""
    # 创建一个2x2的图表网格
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. 特征激活值随时间变化
    t = np.linspace(0, 20, 100)
    input_activation = 0.3 + 0.2 * np.sin(t/2)
    output_activation = 0.5 + 0.4 * np.sin(t/2 + 0.5)
    
    axes[0, 0].plot(t, input_activation, linewidth=2, label='Before Attention')
    axes[0, 0].plot(t, output_activation, linewidth=2, label='After Attention')
    axes[0, 0].set_title('Feature Activation Over Time', fontsize=12)
    axes[0, 0].set_xlabel('Time Steps', fontsize=10)
    axes[0, 0].set_ylabel('Average Activation', fontsize=10)
    axes[0, 0].legend()
    axes[0, 0].grid(True, linestyle='--', alpha=0.7)
    
    # 2. 输入特征热力图
    input_feature = np.zeros((20, 20))
    # 创建一个随机的特征图
    input_feature = np.random.rand(20, 20) * 0.5
    # 添加一些结构
    x, y = np.meshgrid(np.linspace(-2, 2, 20), np.linspace(-2, 2, 20))
    d = np.sqrt(x*x + y*y)
    input_feature += 0.5 * np.exp(-(d**2 / 8))
    
    im = axes[0, 1].imshow(input_feature, cmap='viridis')
    axes[0, 1].set_title('Input Feature Map', fontsize=12)
    axes[0, 1].set_xticks([])
    axes[0, 1].set_yticks([])
    fig.colorbar(im, ax=axes[0, 1])
    
    # 3. 注意力加权后的特征热力图
    attention_weight = np.zeros((20, 20))
    # 创建一个中心高，边缘低的高斯分布作为注意力权重
    attention_weight = np.exp(-(d**2 / 4))
    
    im = axes[1, 0].imshow(attention_weight, cmap='hot')
    axes[1, 0].set_title('Attention Weight Map', fontsize=12)
    axes[1, 0].set_xticks([])
    axes[1, 0].set_yticks([])
    fig.colorbar(im, ax=axes[1, 0])
    
    # 4. 输出特征热力图
    output_feature = input_feature * attention_weight
    
    im = axes[1, 1].imshow(output_feature, cmap='viridis')
    axes[1, 1].set_title('Output Feature Map (After Attention)', fontsize=12)
    axes[1, 1].set_xticks([])
    axes[1, 1].set_yticks([])
    fig.colorbar(im, ax=axes[1, 1])
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'feature_comparison.png'), dpi=300)
    plt.close()

def main():
    """主函数"""
    # 生成各种可视化
    generate_attention_mechanism_figure()
    generate_window_weights_visualization()
    generate_temporal_gate_visualization()
    generate_feature_comparison()
    
    print(f"所有可视化已保存到 {output_dir}")

if __name__ == "__main__":
    main()
