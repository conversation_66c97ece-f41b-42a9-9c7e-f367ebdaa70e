import os
import argparse
import torch
import torch.nn as nn
import yaml
import numpy as np
from collections import OrderedDict

# 导入项目模块
import utils
from slr_network import SLRModel
from modules.attention import DynamicTemporalAttention

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', default='./configs/baseline.yaml', help='path to config file')
    parser.add_argument('--dataset', default='phoenix2014-T', help='dataset name')
    parser.add_argument('--device', type=int, default=0, help='GPU device ID')
    parser.add_argument('--checkpoint', type=str, default=None, help='path to checkpoint')
    parser.add_argument('--max_window_size', type=int, default=9, help='maximum window size for attention')
    parser.add_argument('--kernel_sizes', type=str, default='3,5,7', help='kernel sizes for temporal convolution')
    parser.add_argument('--reduction_ratio', type=int, default=16, help='channel reduction ratio')
    return parser.parse_args()

def print_model_params(model):
    """打印模型参数信息"""
    total_params = 0
    attention_params = 0
    
    for name, module in model.named_modules():
        if isinstance(module, DynamicTemporalAttention):
            print(f"\n注意力模块: {name}")
            print(f"  通道数: {module.channels}")
            print(f"  最大窗口大小: {module.max_window_size}")
            print(f"  卷积核大小: {module.kernel_sizes}")
            print(f"  通道缩减比例: {module.reduction_ratio}")
            
            # 计算参数量
            module_params = sum(p.numel() for p in module.parameters() if p.requires_grad)
            attention_params += module_params
            print(f"  参数量: {module_params:,}")
    
    # 计算总参数量
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"\n总参数量: {total_params:,}")
    print(f"注意力模块参数量: {attention_params:,} ({attention_params/total_params*100:.2f}%)")

def main():
    args = parse_args()
    
    # 加载配置
    with open(args.config, 'r') as f:
        config = yaml.load(f, Loader=yaml.FullLoader)
    
    # 加载数据集信息
    with open(f"./configs/{args.dataset}.yaml", 'r') as f:
        dataset_info = yaml.load(f, Loader=yaml.FullLoader)
    
    # 设置设备
    device = utils.GpuDataParallel()
    device.set_device(args.device)
    
    # 加载词汇表
    dict_path = f'./preprocess/{args.dataset}/gloss_dict.npy'
    gloss_dict = np.load(dict_path, allow_pickle=True).item()
    
    # 解析卷积核大小
    kernel_sizes = [int(k) for k in args.kernel_sizes.split(',')]
    
    # 创建模型
    model = SLRModel(
        num_classes=len(gloss_dict)+1,
        c2d_type='resnet18',
        conv_type=2,
        use_bn=1,
        gloss_dict=gloss_dict,
        loss_weights={'ConvCTC': 1.0, 'SeqCTC': 1.0, 'Dist': 25.0}
    )
    
    # 加载预训练权重
    if args.checkpoint:
        state_dict = torch.load(args.checkpoint)['model_state_dict']
        state_dict = OrderedDict([(k.replace('.module', ''), v) for k, v in state_dict.items()])
        model.load_state_dict(state_dict, strict=False)
    
    # 打印模型参数信息
    print_model_params(model)
    
    # 将模型移至GPU
    model = model.to(device.output_device)
    
    print("\n模型结构:")
    print(model)

if __name__ == '__main__':
    main()
