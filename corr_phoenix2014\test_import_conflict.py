#!/usr/bin/env python3
"""
Test import conflicts
"""

import sys
import inspect

def test_import_order():
    """Test different import orders"""
    print("=== Testing Import Order ===")
    
    # Clear any cached modules
    modules_to_clear = [
        'modules.resnet',
        'torchvision.models.resnet',
        'torchvision.models',
        'slr_network'
    ]
    
    for module in modules_to_clear:
        if module in sys.modules:
            del sys.modules[module]
            print(f"Cleared: {module}")
    
    print("\n1. Import torchvision first, then local resnet:")
    try:
        import torchvision.models as models
        import modules.resnet as resnet
        
        print(f"torchvision ResNet: {models.resnet.ResNet}")
        print(f"local ResNet: {resnet.ResNet}")
        
        # Check signatures
        torch_sig = inspect.signature(models.resnet.ResNet.__init__)
        local_sig = inspect.signature(resnet.ResNet.__init__)
        
        print(f"torchvision ResNet signature: {torch_sig}")
        print(f"local ResNet signature: {local_sig}")
        
        # Test creation
        attention_params = {'test': True}
        
        # This should fail
        try:
            torch_model = models.resnet.ResNet(models.resnet.BasicBlock, [2, 2, 2, 2], attention_params=attention_params)
            print("✗ Unexpected: torchvision ResNet accepted attention_params")
        except TypeError as e:
            print(f"✓ Expected: torchvision ResNet rejected attention_params: {e}")
        
        # This should work
        try:
            local_model = resnet.ResNet(resnet.BasicBlock, [2, 2, 2, 2], attention_params=attention_params)
            print("✓ Expected: local ResNet accepted attention_params")
        except TypeError as e:
            print(f"✗ Unexpected: local ResNet rejected attention_params: {e}")
        
        # Test resnet18 function
        try:
            model = resnet.resnet18(attention_params=attention_params)
            print("✓ resnet18 function works")
        except Exception as e:
            print(f"✗ resnet18 function failed: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        import traceback
        traceback.print_exc()


def test_slr_model_import():
    """Test SLRModel import specifically"""
    print("\n=== Testing SLRModel Import ===")
    
    # Clear modules
    modules_to_clear = [
        'slr_network',
        'modules.resnet'
    ]
    
    for module in modules_to_clear:
        if module in sys.modules:
            del sys.modules[module]
    
    try:
        # Import SLRModel
        from slr_network import SLRModel
        
        print("✓ SLRModel imported successfully")
        
        # Test creation without attention_params
        print("Testing SLRModel without attention_params...")
        model1 = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)
        print("✓ SLRModel created without attention_params")
        
        # Test creation with attention_params
        print("Testing SLRModel with attention_params...")
        attention_params = {
            'max_window_size': 9,
            'kernel_sizes': [3, 5, 7],
            'reduction_ratio': 16
        }
        model2 = SLRModel(
            num_classes=1000, 
            c2d_type='resnet18', 
            conv_type=2, 
            use_bn=1,
            attention_params=attention_params
        )
        print("✓ SLRModel created with attention_params")
        
    except Exception as e:
        print(f"✗ SLRModel test failed: {e}")
        import traceback
        traceback.print_exc()


def test_direct_resnet_call():
    """Test calling resnet18 directly"""
    print("\n=== Testing Direct ResNet Call ===")
    
    try:
        # Clear modules
        if 'modules.resnet' in sys.modules:
            del sys.modules['modules.resnet']
        
        # Import fresh
        import modules.resnet as resnet
        
        print("Testing resnet18() without params...")
        model1 = resnet.resnet18()
        print("✓ resnet18() works without params")
        
        print("Testing resnet18() with attention_params...")
        attention_params = {
            'max_window_size': 9,
            'kernel_sizes': [3, 5, 7],
            'reduction_ratio': 16
        }
        model2 = resnet.resnet18(attention_params=attention_params)
        print("✓ resnet18() works with attention_params")
        
        # Check if DTA modules exist
        if hasattr(model2, 'corr1') and hasattr(model2, 'corr2'):
            print("✓ DynamicTemporalAttention modules found")
        else:
            print("✗ DynamicTemporalAttention modules not found")
        
    except Exception as e:
        print(f"✗ Direct ResNet call failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    print("=== Import Conflict Test ===\n")
    
    test_functions = [
        test_import_order,
        test_direct_resnet_call,
        test_slr_model_import
    ]
    
    for func in test_functions:
        try:
            func()
        except Exception as e:
            print(f"Error in {func.__name__}: {e}")
        print()


if __name__ == "__main__":
    main()
