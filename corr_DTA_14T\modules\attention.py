
# 在modules/attention.py中实现新的注意力模块
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import os

class DynamicTemporalAttention(nn.Module):
    def __init__(self, channels, max_window_size=9, kernel_sizes=None, reduction_ratio=16):
        super().__init__()
        self.channels = channels
        self.max_window_size = max_window_size
        self.reduction_ratio = reduction_ratio

        # 默认卷积核大小
        if kernel_sizes is None:
            kernel_sizes = [3, 5, 7]
        self.kernel_sizes = kernel_sizes

        # 窗口大小预测
        self.window_pool = nn.AdaptiveAvgPool3d((None, 1, 1))
        self.window_predictor = nn.Sequential(
            nn.Conv1d(channels, channels//reduction_ratio, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv1d(channels//reduction_ratio, max_window_size, kernel_size=1),
            nn.Softmax(dim=1)
        )

        # 多窗口卷积 - 使用指定的卷积核大小
        if len(kernel_sizes) > 0:
            self.window_convs = nn.ModuleList([
                nn.Conv3d(
                    channels, channels,
                    kernel_size=(k, 1, 1),
                    padding=(k//2, 0, 0),
                    groups=channels
                ) for k in kernel_sizes
            ])
        else:
            # 如果没有指定卷积核大小，则使用默认的方式
            self.window_convs = nn.ModuleList([
                nn.Conv3d(
                    channels, channels,
                    kernel_size=(2*i+1, 1, 1),
                    padding=(i, 0, 0),
                    groups=channels
                ) for i in range(1, max_window_size+1)
            ])

        # 特征融合
        self.fusion = nn.Sequential(
            nn.Conv3d(channels, channels, kernel_size=1),
            nn.BatchNorm3d(channels),
            nn.ReLU(inplace=True)
        )

        # 时间门控
        self.temporal_gate = nn.Sequential(
            nn.Conv3d(channels, channels, kernel_size=(5, 1, 1), padding=(2, 0, 0)),
            nn.Sigmoid()
        )

        # 用于可视化的中间结果存储
        self.intermediate_results = {}

    def forward(self, x, save_intermediates=False):
        B, C, T, H, W = x.shape

        # 如果需要保存中间结果用于可视化
        if save_intermediates:
            self.intermediate_results['input'] = x.detach()

        # 预测每个位置的窗口大小权重
        x_pool = self.window_pool(x)  # [B, C, T, 1, 1]
        x_pool = x_pool.squeeze(-1).squeeze(-1)  # [B, C, T]

        # 如果使用指定的卷积核大小
        if len(self.kernel_sizes) > 0:
            # 直接应用每个卷积核
            multi_scale_feats = []
            kernel_feats = []  # 存储每个卷积核的输出，用于可视化

            for i, conv in enumerate(self.window_convs):
                feat = conv(x)
                multi_scale_feats.append(feat)
                if save_intermediates:
                    kernel_feats.append(feat.detach())

            # 平均融合多尺度特征
            fused_feat = sum(multi_scale_feats) / len(multi_scale_feats)

            if save_intermediates:
                self.intermediate_results['kernel_feats'] = kernel_feats
                self.intermediate_results['fused_feat'] = fused_feat.detach()
        else:
            # 使用动态窗口大小预测
            window_weights = self.window_predictor(x_pool)  # [B, max_window_size, T]

            if save_intermediates:
                self.intermediate_results['window_weights'] = window_weights.detach()

            # 应用不同窗口大小的卷积
            multi_scale_feats = []
            weighted_feats = []  # 存储加权后的特征，用于可视化

            for i, conv in enumerate(self.window_convs):
                feat = conv(x)
                # 应用对应窗口的权重
                weight = window_weights[:, i:i+1, :].unsqueeze(-1).unsqueeze(-1)  # [B, 1, T, 1, 1]
                weighted_feat = feat * weight
                multi_scale_feats.append(weighted_feat)

                if save_intermediates:
                    weighted_feats.append(weighted_feat.detach())

            # 融合多尺度特征
            fused_feat = sum(multi_scale_feats)

            if save_intermediates:
                self.intermediate_results['weighted_feats'] = weighted_feats
                self.intermediate_results['fused_feat'] = fused_feat.detach()

        # 应用时间门控
        gate = self.temporal_gate(fused_feat)
        out = fused_feat * gate

        if save_intermediates:
            self.intermediate_results['temporal_gate'] = gate.detach()
            self.intermediate_results['gated_feat'] = out.detach()

        # 最终融合
        out = self.fusion(out)

        if save_intermediates:
            self.intermediate_results['output'] = out.detach()

        return out

    def visualize_attention(self, output_dir='./attention_vis', prefix=''):
        """可视化注意力模块的中间结果"""
        if not self.intermediate_results:
            print("没有中间结果可供可视化。请在前向传播时设置 save_intermediates=True")
            return

        os.makedirs(output_dir, exist_ok=True)

        # 1. 可视化窗口权重
        if 'window_weights' in self.intermediate_results:
            window_weights = self.intermediate_results['window_weights'].cpu().numpy()
            plt.figure(figsize=(10, 6))
            for i in range(min(window_weights.shape[1], 5)):  # 最多显示5个窗口
                plt.plot(window_weights[0, i, :], label=f'Kernel {self.kernel_sizes[i]}')
            plt.title('Dynamic Window Weights')
            plt.xlabel('Time Step')
            plt.ylabel('Weight')
            plt.legend()
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'{prefix}window_weights.png'), dpi=300)
            plt.close()

        # 2. 可视化时间门控
        if 'temporal_gate' in self.intermediate_results:
            gate = self.intermediate_results['temporal_gate'].cpu().numpy()
            # 计算每个时间步的平均门控值
            avg_gate = np.mean(gate[0], axis=(0, 2, 3))
            plt.figure(figsize=(10, 4))
            plt.plot(avg_gate)
            plt.title('Temporal Gate Values')
            plt.xlabel('Time Step')
            plt.ylabel('Gate Value')
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'{prefix}temporal_gate.png'), dpi=300)
            plt.close()

            # 为几个时间步创建热力图
            for t in range(min(gate.shape[2], 5)):  # 最多显示5个时间步
                gate_map = np.mean(gate[0, :, t, :, :], axis=0)
                plt.figure(figsize=(6, 6))
                plt.imshow(gate_map, cmap='viridis')
                plt.colorbar()
                plt.title(f'Gate Heatmap - Time {t}')
                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, f'{prefix}gate_heatmap_t{t}.png'), dpi=300)
                plt.close()

        # 3. 可视化不同卷积核的贡献
        if 'kernel_feats' in self.intermediate_results:
            kernel_feats = self.intermediate_results['kernel_feats']
            # 为每个卷积核创建一个平均激活图
            for i, feat in enumerate(kernel_feats):
                feat_np = feat.cpu().numpy()
                # 计算每个时间步的平均激活
                avg_feat = np.mean(feat_np[0], axis=(0, 2, 3))
                plt.figure(figsize=(10, 4))
                plt.plot(avg_feat)
                plt.title(f'Kernel Size {self.kernel_sizes[i]} Activation')
                plt.xlabel('Time Step')
                plt.ylabel('Activation')
                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, f'{prefix}kernel{self.kernel_sizes[i]}_activation.png'), dpi=300)
                plt.close()

        # 4. 可视化注意力前后的特征对比
        if 'input' in self.intermediate_results and 'output' in self.intermediate_results:
            input_feat = self.intermediate_results['input'].cpu().numpy()
            output_feat = self.intermediate_results['output'].cpu().numpy()

            # 计算每个时间步的平均激活
            avg_input = np.mean(input_feat[0], axis=(0, 2, 3))
            avg_output = np.mean(output_feat[0], axis=(0, 2, 3))

            plt.figure(figsize=(12, 6))
            plt.plot(avg_input, label='Input')
            plt.plot(avg_output, label='Output')
            plt.title('Feature Comparison Before and After Attention')
            plt.xlabel('Time Step')
            plt.ylabel('Average Activation')
            plt.legend()
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'{prefix}feature_comparison.png'), dpi=300)
            plt.close()

        print(f"注意力可视化已保存到 {output_dir}")

    def get_attention_weights(self):
        """获取注意力权重用于外部可视化"""
        if not self.intermediate_results:
            return None

        return {
            'window_weights': self.intermediate_results.get('window_weights', None),
            'temporal_gate': self.intermediate_results.get('temporal_gate', None),
            'kernel_feats': self.intermediate_results.get('kernel_feats', None),
            'input': self.intermediate_results.get('input', None),
            'output': self.intermediate_results.get('output', None)
        }
