<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" autoUpload="Always" serverName="<EMAIL>:22333 password (3)" remoteFilesAllowedToDisappearOnAutoupload="false" showNewOnTargetInSync="false">
    <serverData>
      <paths name="<EMAIL>:16099 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:22333 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:22333 password (2)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:22333 password (3)">
        <serverdata>
          <mappings>
            <mapping deploy="/root/CorrNet-main" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:55067 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
    </serverData>
    <option name="myAutoUpload" value="ALWAYS" />
  </component>
</project>