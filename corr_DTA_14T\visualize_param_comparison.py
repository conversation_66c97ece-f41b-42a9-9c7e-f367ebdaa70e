"""
可视化不同注意力参数配置的性能对比
"""
import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import re
import glob
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，适合服务器环境

# 配置参数
results_dir = './param_tuning_results'  # 参数调优结果目录
output_dir = './paper_figures/param_comparison'  # 输出目录

# 创建输出目录
os.makedirs(output_dir, exist_ok=True)

def parse_results_files():
    """解析结果文件，提取性能指标"""
    results = []
    
    # 遍历所有结果目录
    for result_dir in glob.glob(os.path.join(results_dir, 'ws*')):
        # 提取参数配置
        dir_name = os.path.basename(result_dir)
        match = re.match(r'ws(\d+)_ks(.+)_rr(\d+)', dir_name)
        
        if match:
            window_size = int(match.group(1))
            kernel_sizes = match.group(2).replace('_', ',')
            reduction_ratio = int(match.group(3))
            
            # 读取结果文件
            result_file = os.path.join(result_dir, 'results.txt')
            if os.path.exists(result_file):
                with open(result_file, 'r') as f:
                    content = f.read()
                    # 提取WER (Word Error Rate)
                    wer_match = re.search(r'WER:\s+([\d.]+)', content)
                    if wer_match:
                        wer = float(wer_match.group(1))
                        
                        # 添加到结果列表
                        results.append({
                            'window_size': window_size,
                            'kernel_sizes': kernel_sizes,
                            'reduction_ratio': reduction_ratio,
                            'wer': wer
                        })
    
    return pd.DataFrame(results)

def visualize_window_size_impact(df):
    """可视化窗口大小对性能的影响"""
    plt.figure(figsize=(10, 6))
    
    # 按窗口大小分组，计算平均WER
    window_size_impact = df.groupby('window_size')['wer'].mean().reset_index()
    
    # 绘制条形图
    sns.barplot(x='window_size', y='wer', data=window_size_impact)
    plt.title('窗口大小对识别性能的影响', fontsize=14)
    plt.xlabel('最大窗口大小', fontsize=12)
    plt.ylabel('平均词错误率 (WER %)', fontsize=12)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 添加数值标签
    for i, row in enumerate(window_size_impact.itertuples()):
        plt.text(i, row.wer + 0.2, f'{row.wer:.2f}%', ha='center')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'window_size_impact.png'), dpi=300)
    plt.close()

def visualize_kernel_sizes_impact(df):
    """可视化卷积核大小对性能的影响"""
    plt.figure(figsize=(12, 6))
    
    # 按卷积核大小分组，计算平均WER
    kernel_sizes_impact = df.groupby('kernel_sizes')['wer'].mean().reset_index()
    
    # 绘制条形图
    sns.barplot(x='kernel_sizes', y='wer', data=kernel_sizes_impact)
    plt.title('卷积核大小组合对识别性能的影响', fontsize=14)
    plt.xlabel('卷积核大小组合', fontsize=12)
    plt.ylabel('平均词错误率 (WER %)', fontsize=12)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.xticks(rotation=45)
    
    # 添加数值标签
    for i, row in enumerate(kernel_sizes_impact.itertuples()):
        plt.text(i, row.wer + 0.2, f'{row.wer:.2f}%', ha='center')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'kernel_sizes_impact.png'), dpi=300)
    plt.close()

def visualize_reduction_ratio_impact(df):
    """可视化降维比例对性能的影响"""
    plt.figure(figsize=(10, 6))
    
    # 按降维比例分组，计算平均WER
    reduction_ratio_impact = df.groupby('reduction_ratio')['wer'].mean().reset_index()
    
    # 绘制条形图
    sns.barplot(x='reduction_ratio', y='wer', data=reduction_ratio_impact)
    plt.title('降维比例对识别性能的影响', fontsize=14)
    plt.xlabel('降维比例', fontsize=12)
    plt.ylabel('平均词错误率 (WER %)', fontsize=12)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 添加数值标签
    for i, row in enumerate(reduction_ratio_impact.itertuples()):
        plt.text(i, row.wer + 0.2, f'{row.wer:.2f}%', ha='center')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'reduction_ratio_impact.png'), dpi=300)
    plt.close()

def visualize_heatmap(df):
    """可视化参数组合热力图"""
    # 创建窗口大小和降维比例的交叉表
    heatmap_data = df.pivot_table(
        index='window_size', 
        columns='reduction_ratio', 
        values='wer',
        aggfunc='mean'
    )
    
    plt.figure(figsize=(10, 8))
    sns.heatmap(heatmap_data, annot=True, fmt='.2f', cmap='YlGnBu_r', cbar_kws={'label': 'WER %'})
    plt.title('窗口大小和降维比例对性能的影响', fontsize=14)
    plt.xlabel('降维比例', fontsize=12)
    plt.ylabel('窗口大小', fontsize=12)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'params_heatmap.png'), dpi=300)
    plt.close()

def visualize_best_config(df):
    """可视化最佳配置"""
    # 找到WER最低的配置
    best_config = df.loc[df['wer'].idxmin()]
    
    # 创建一个表格式的图
    plt.figure(figsize=(8, 4))
    plt.axis('off')
    
    # 表格数据
    table_data = [
        ['参数', '最佳值'],
        ['窗口大小', str(best_config['window_size'])],
        ['卷积核大小', best_config['kernel_sizes']],
        ['降维比例', str(best_config['reduction_ratio'])],
        ['词错误率 (WER)', f"{best_config['wer']:.2f}%"]
    ]
    
    # 创建表格
    table = plt.table(cellText=table_data, loc='center', cellLoc='center', colWidths=[0.4, 0.4])
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1, 2)
    
    # 设置标题
    plt.title('动态时空注意力最佳参数配置', fontsize=16, pad=20)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'best_config.png'), dpi=300)
    plt.close()

def visualize_parameter_interactions(df):
    """可视化参数之间的交互影响"""
    # 创建一个图形网格
    g = sns.PairGrid(df, x_vars=['window_size', 'reduction_ratio'], 
                    y_vars=['wer'], height=4, aspect=1.5)
    
    # 绘制散点图和回归线
    g.map(sns.regplot, scatter_kws={'alpha':0.5, 's':80}, line_kws={'color':'red'})
    
    # 设置标签
    g.set(xlabel='参数值', ylabel='词错误率 (WER %)')
    g.fig.suptitle('参数与性能的关系', fontsize=16, y=1.05)
    
    # 调整子图标签
    for ax, title in zip(g.axes.flat, ['窗口大小', '降维比例']):
        ax.set_title(title)
        ax.grid(True, linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'parameter_interactions.png'), dpi=300)
    plt.close()

def main():
    """主函数"""
    # 解析结果文件
    results_df = parse_results_files()
    
    if len(results_df) == 0:
        print("未找到有效的结果文件！")
        return
    
    print(f"共找到 {len(results_df)} 个有效配置结果")
    
    # 生成各种可视化
    visualize_window_size_impact(results_df)
    visualize_kernel_sizes_impact(results_df)
    visualize_reduction_ratio_impact(results_df)
    visualize_heatmap(results_df)
    visualize_best_config(results_df)
    visualize_parameter_interactions(results_df)
    
    print(f"所有参数对比可视化已保存到 {output_dir}")

if __name__ == "__main__":
    main()
