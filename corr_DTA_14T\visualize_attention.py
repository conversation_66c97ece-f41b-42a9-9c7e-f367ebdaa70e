"""
动态时空注意力可视化脚本
用于生成论文所需的可视化结果
"""
import os
import numpy as np
import torch
import torch.nn as nn
import cv2
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from collections import OrderedDict
import glob
from utils import video_augmentation
from slr_network import SLRModel
import utils
from modules.attention import DynamicTemporalAttention
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，适合服务器环境

# 配置参数
gpu_id = 0  # 使用的GPU ID
dataset = 'phoenix2014-T'  # 支持 [phoenix2014, phoenix2014-T, CSL-Daily]
prefix = './dataset/PHOENIX-2014-T-release-v3/PHOENIX-2014-T'
dict_path = f'./preprocess/{dataset}/gloss_dict.npy'
model_weights = './pretrain_model/dev_18.90_PHOENIX14-T.pt'  # 替换为您的模型路径
select_id = 0  # 选择要可视化的视频索引
output_dir = './paper_visualizations'  # 输出目录

# 创建输出目录
os.makedirs(output_dir, exist_ok=True)

# 加载数据和预处理
def load_data():
    gloss_dict = np.load(dict_path, allow_pickle=True).item()
    inputs_list = np.load(f"./preprocess/{dataset}/dev_info.npy", allow_pickle=True).item()
    name = inputs_list[select_id]['fileid']
    print(f'Generating visualizations for {name}')
    
    # 加载图像
    img_folder = os.path.join(prefix, "features/fullFrame-256x256px/" + inputs_list[select_id]['folder']) if 'phoenix' in dataset else os.path.join(prefix, inputs_list[select_id]['folder'])
    img_list = sorted(glob.glob(img_folder))
    img_list = [cv2.cvtColor(cv2.imread(img_path), cv2.COLOR_BGR2RGB) for img_path in img_list]
    
    # 处理标签
    label_list = []
    for phase in inputs_list[select_id]['label'].split(" "):
        if phase == '':
            continue
        if phase in gloss_dict.keys():
            label_list.append(gloss_dict[phase][0])
    
    # 数据转换
    transform = video_augmentation.Compose([
        video_augmentation.CenterCrop(224),
        video_augmentation.Resize(1.0),
        video_augmentation.ToTensor(),
    ])
    vid, label = transform(img_list, label_list, None)
    vid = vid.float() / 127.5 - 1
    vid = vid.unsqueeze(0)
    
    # 填充处理
    left_pad = 0
    last_stride = 1
    total_stride = 1
    kernel_sizes = ['K5', "P2", 'K5', "P2"]
    for layer_idx, ks in enumerate(kernel_sizes):
        if ks[0] == 'K':
            left_pad = left_pad * last_stride 
            left_pad += int((int(ks[1])-1)/2)
        elif ks[0] == 'P':
            last_stride = int(ks[1])
            total_stride = total_stride * last_stride
    
    max_len = vid.size(1)
    video_length = torch.LongTensor([
        int(np.ceil(max_len / total_stride)) * total_stride + 2 * left_pad
    ])
    right_pad = int(np.ceil(max_len / total_stride)) * total_stride - max_len + left_pad
    max_len = max_len + left_pad + right_pad
    vid = torch.cat(
        (
            vid[0,0][None].expand(left_pad, -1, -1, -1),
            vid[0],
            vid[0,-1][None].expand(max_len - vid.size(1) - left_pad, -1, -1, -1),
        ), dim=0).unsqueeze(0)
    
    return vid, video_length, label_list, gloss_dict, img_list

# 初始化模型
def init_model(gloss_dict):
    device = utils.GpuDataParallel()
    device.set_device(gpu_id)
    
    # 创建带有注意力参数的模型
    attention_params = {
        'max_window_size': 11,
        'kernel_sizes': [3, 5, 7],
        'reduction_ratio': 16
    }
    
    model = SLRModel(
        num_classes=len(gloss_dict)+1,
        c2d_type='resnet18',
        conv_type=2,
        use_bn=1,
        gloss_dict=gloss_dict,
        loss_weights={'ConvCTC': 1.0, 'SeqCTC': 1.0, 'Dist': 25.0},
        attention_params=attention_params
    )
    
    # 加载预训练权重
    state_dict = torch.load(model_weights)['model_state_dict']
    state_dict = OrderedDict([(k.replace('.module', ''), v) for k, v in state_dict.items()])
    model.load_state_dict(state_dict, strict=True)
    model = model.to(device.output_device)
    model.eval()  # 设置为评估模式
    
    return model, device

# 可视化注意力权重
def visualize_attention_weights(model, vid, vid_lgt, device):
    """可视化动态时空注意力的权重"""
    # 收集注意力模块的激活
    activations = {}
    
    def get_activation(name):
        def hook(module, input, output):
            activations[name] = output.detach()
        return hook
    
    # 注册钩子
    hooks = []
    attention_modules = []
    for name, module in model.named_modules():
        if isinstance(module, DynamicTemporalAttention):
            attention_modules.append((name, module))
            # 注册钩子到不同的组件
            hooks.append(module.window_predictor.register_forward_hook(get_activation(f"{name}_window_weights")))
            hooks.append(module.temporal_gate.register_forward_hook(get_activation(f"{name}_temporal_gate")))
            hooks.append(module.register_forward_hook(get_activation(f"{name}_output")))
    
    # 前向传播
    with torch.no_grad():
        vid = device.data_to_device(vid)
        vid_lgt = device.data_to_device(vid_lgt)
        ret_dict = model(vid, vid_lgt)
    
    # 移除钩子
    for hook in hooks:
        hook.remove()
    
    # 可视化每个注意力模块的权重
    for name, module in attention_modules:
        # 1. 可视化窗口权重
        if f"{name}_window_weights" in activations:
            window_weights = activations[f"{name}_window_weights"].cpu().numpy()
            plt.figure(figsize=(12, 6))
            for i in range(min(5, window_weights.shape[1])):  # 只显示前5个窗口大小
                plt.plot(window_weights[0, i, :], label=f'Window {i+1}')
            plt.title(f'Dynamic Window Weights - {name}')
            plt.xlabel('Time Step')
            plt.ylabel('Weight')
            plt.legend()
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f"{name}_window_weights.png"), dpi=300)
            plt.close()
        
        # 2. 可视化时间门控
        if f"{name}_temporal_gate" in activations:
            gate = activations[f"{name}_temporal_gate"].cpu().numpy()
            # 计算每个时间步的平均门控值
            avg_gate = np.mean(gate[0], axis=(0, 2, 3))
            plt.figure(figsize=(12, 4))
            plt.plot(avg_gate)
            plt.title(f'Temporal Gate Values - {name}')
            plt.xlabel('Time Step')
            plt.ylabel('Gate Value')
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f"{name}_temporal_gate.png"), dpi=300)
            plt.close()
            
            # 创建热力图
            for t in range(min(5, gate.shape[2])):  # 只显示前5个时间步
                gate_map = np.mean(gate[0, :, t, :, :], axis=0)
                plt.figure(figsize=(6, 6))
                sns.heatmap(gate_map, cmap='viridis')
                plt.title(f'Gate Heatmap - {name} - Time {t}')
                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, f"{name}_gate_heatmap_t{t}.png"), dpi=300)
                plt.close()

# 可视化注意力前后的特征对比
def visualize_feature_comparison(model, vid, vid_lgt, device, original_frames):
    """可视化注意力前后的特征对比"""
    # 收集特征
    features = {}
    
    def get_feature(name):
        def hook(module, input, output):
            features[name] = output.detach()
        return hook
    
    # 注册钩子
    hooks = []
    # 在每个注意力模块前后注册钩子
    for name, module in model.named_modules():
        if isinstance(module, DynamicTemporalAttention):
            # 找到该模块的父模块
            parent_name = name.rsplit('.', 1)[0]
            parent = model
            for part in parent_name.split('.'):
                parent = getattr(parent, part)
            
            # 注册钩子到注意力模块前后
            hooks.append(module.register_forward_hook(get_feature(f"{name}_output")))
    
    # 前向传播
    with torch.no_grad():
        vid = device.data_to_device(vid)
        vid_lgt = device.data_to_device(vid_lgt)
        ret_dict = model(vid, vid_lgt)
    
    # 移除钩子
    for hook in hooks:
        hook.remove()
    
    # 可视化特征对比
    for name in features:
        if "_output" in name:
            feature = features[name].cpu().numpy()
            
            # 为每个时间步创建特征可视化
            for t in range(min(5, feature.shape[2])):  # 只显示前5个时间步
                # 计算特征图的平均激活
                feat_map = np.mean(feature[0, :, t, :, :], axis=0)
                feat_map = cv2.resize(feat_map, (224, 224))
                feat_map = (feat_map - np.min(feat_map)) / (np.max(feat_map) - np.min(feat_map) + 1e-8)
                
                # 创建热力图
                heatmap = cv2.applyColorMap((feat_map * 255).astype(np.uint8), cv2.COLORMAP_JET)
                
                # 如果有原始帧，则叠加显示
                if t < len(original_frames):
                    orig_frame = original_frames[t]
                    orig_frame = cv2.resize(orig_frame, (224, 224))
                    
                    # 创建叠加图像
                    alpha = 0.6
                    overlay = cv2.addWeighted(orig_frame, 1-alpha, heatmap, alpha, 0)
                    
                    # 保存结果
                    cv2.imwrite(os.path.join(output_dir, f"{name}_overlay_t{t}.jpg"), 
                                cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR))

# 主函数
def main():
    # 加载数据
    vid, video_length, label_list, gloss_dict, original_frames = load_data()
    
    # 初始化模型
    model, device = init_model(gloss_dict)
    
    # 可视化注意力权重
    visualize_attention_weights(model, vid, video_length, device)
    
    # 可视化注意力前后的特征对比
    visualize_feature_comparison(model, vid, video_length, device, original_frames)
    
    print(f"所有可视化已保存到 {output_dir}")

if __name__ == "__main__":
    main()
