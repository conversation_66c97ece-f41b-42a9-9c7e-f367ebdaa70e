name: old_slt
channels:
  - defaults
dependencies:
  - _ipyw_jlab_nb_ext_conf=0.1.0=py39h06a4308_0
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=4.5=1_gnu
  - alabaster=0.7.12=pyhd3eb1b0_0
  - anaconda-client=1.9.0=py39h06a4308_0
  - anaconda-project=0.10.1=pyhd3eb1b0_0
  - anyio=2.2.0=py39h06a4308_1
  - appdirs=1.4.4=pyhd3eb1b0_0
  - argh=0.26.2=py39h06a4308_0
  - argon2-cffi=20.1.0=py39h27cfd23_1
  - arrow=0.13.1=py39h06a4308_0
  - asn1crypto=1.4.0=py_0
  - astroid=2.6.6=py39h06a4308_0
  - astropy=4.3.1=py39h09021b7_0
  - async_generator=1.10=pyhd3eb1b0_0
  - atomicwrites=1.4.0=py_0
  - attrs=21.2.0=pyhd3eb1b0_0
  - autopep8=1.5.7=pyhd3eb1b0_0
  - babel=2.9.1=pyhd3eb1b0_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - backports=1.0=pyhd3eb1b0_2
  - backports.functools_lru_cache=1.6.4=pyhd3eb1b0_0
  - backports.shutil_get_terminal_size=1.0.0=pyhd3eb1b0_3
  - backports.tempfile=1.0=pyhd3eb1b0_1
  - backports.weakref=1.0.post1=py_1
  - beautifulsoup4=4.10.0=pyh06a4308_0
  - binaryornot=0.4.4=pyhd3eb1b0_1
  - bitarray=2.3.0=py39h7f8727e_1
  - bkcharts=0.2=py39h06a4308_0
  - black=19.10b0=py_0
  - blas=1.0=mkl
  - bleach=4.0.0=pyhd3eb1b0_0
  - blosc=1.21.0=h8c45485_0
  - bokeh=2.4.1=py39h06a4308_0
  - boto=2.49.0=py39h06a4308_0
  - bottleneck=1.3.2=py39hdd57654_1
  - brotli=1.0.9=he6710b0_2
  - brotlipy=0.7.0=py39h27cfd23_1003
  - brunsli=0.1=h2531618_0
  - bzip2=1.0.8=h7b6447c_0
  - c-ares=1.17.1=h27cfd23_0
  - ca-certificates=2021.10.26=h06a4308_2
  - cached-property=1.5.2=py_0
  - cairo=1.16.0=hf32fb01_1
  - certifi=2021.10.8=py39h06a4308_0
  - cffi=1.14.6=py39h400218f_0
  - cfitsio=3.470=hf0d0db6_6
  - chardet=4.0.0=py39h06a4308_1003
  - charls=2.2.0=h2531618_0
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - click=8.0.3=pyhd3eb1b0_0
  - cloudpickle=2.0.0=pyhd3eb1b0_0
  - clyent=1.2.2=py39h06a4308_1
  - colorama=0.4.4=pyhd3eb1b0_0
  - conda-content-trust=0.1.1=pyhd3eb1b0_0
  - conda-pack=0.6.0=pyhd3eb1b0_0
  - conda-package-handling=1.7.3=py39h27cfd23_1
  - conda-repo-cli=1.0.4=pyhd3eb1b0_0
  - conda-verify=3.4.2=py_1
  - contextlib2=0.6.0.post1=pyhd3eb1b0_0
  - cookiecutter=1.7.2=pyhd3eb1b0_0
  - cryptography=3.4.8=py39hd23ed53_0
  - curl=7.78.0=h1ccaba5_0
  - cycler=0.10.0=py39h06a4308_0
  - cython=0.29.24=py39hdbfa776_0
  - cytoolz=0.11.0=py39h27cfd23_0
  - daal4py=2021.3.0=py39hae6d005_0
  - dal=2021.3.0=h06a4308_557
  - dask=2021.10.0=pyhd3eb1b0_0
  - dask-core=2021.10.0=pyhd3eb1b0_0
  - dataclasses=0.8=pyh6d0b6a4_7
  - dbus=1.13.18=hb2f20db_0
  - debugpy=1.4.1=py39h295c915_0
  - decorator=5.1.0=pyhd3eb1b0_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - diff-match-patch=20200713=pyhd3eb1b0_0
  - distributed=2021.10.0=py39h06a4308_0
  - docutils=0.17.1=py39h06a4308_1
  - entrypoints=0.3=py39h06a4308_0
  - et_xmlfile=1.1.0=py39h06a4308_0
  - expat=2.4.1=h2531618_2
  - fastcache=1.1.0=py39he8ac12f_0
  - filelock=3.3.1=pyhd3eb1b0_1
  - flake8=3.9.2=pyhd3eb1b0_0
  - flask=1.1.2=pyhd3eb1b0_0
  - fontconfig=2.13.1=h6c09931_0
  - fonttools=4.25.0=pyhd3eb1b0_0
  - freetype=2.10.4=h5ab3b9f_0
  - fribidi=1.0.10=h7b6447c_0
  - future=0.18.2=py39h06a4308_1
  - get_terminal_size=1.0.0=haa9412d_0
  - gevent=21.8.0=py39h7f8727e_1
  - giflib=5.2.1=h7b6447c_0
  - glib=2.69.1=h5202010_0
  - glob2=0.7=pyhd3eb1b0_0
  - gmp=6.2.1=h2531618_2
  - gmpy2=2.0.8=py39h8083e48_3
  - graphite2=1.3.14=h23475e2_0
  - greenlet=1.1.1=py39h295c915_0
  - gst-plugins-base=1.14.0=h8213a91_2
  - gstreamer=1.14.0=h28cd5cc_2
  - h5py=3.3.0=py39h930cdd6_0
  - harfbuzz=2.8.1=h6f93f22_0
  - hdf5=1.10.6=hb1b8bf9_0
  - heapdict=1.0.1=pyhd3eb1b0_0
  - html5lib=1.1=pyhd3eb1b0_0
  - icu=58.2=he6710b0_3
  - idna=3.2=pyhd3eb1b0_0
  - imagecodecs=2021.8.26=py39h4cda21f_0
  - imageio=2.9.0=pyhd3eb1b0_0
  - imagesize=1.2.0=pyhd3eb1b0_0
  - importlib-metadata=4.8.1=py39h06a4308_0
  - importlib_metadata=4.8.1=hd3eb1b0_0
  - inflection=0.5.1=py39h06a4308_0
  - iniconfig=1.1.1=pyhd3eb1b0_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - intervaltree=3.1.0=pyhd3eb1b0_0
  - ipykernel=6.4.1=py39h06a4308_1
  - ipython=7.29.0=py39hb070fc8_0
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - ipywidgets=7.6.5=pyhd3eb1b0_1
  - isort=5.9.3=pyhd3eb1b0_0
  - itsdangerous=2.0.1=pyhd3eb1b0_0
  - jbig=2.1=hdba287a_0
  - jdcal=1.4.1=pyhd3eb1b0_0
  - jedi=0.18.0=py39h06a4308_1
  - jeepney=0.7.1=pyhd3eb1b0_0
  - jinja2=2.11.3=pyhd3eb1b0_0
  - jinja2-time=0.2.0=pyhd3eb1b0_2
  - joblib=1.1.0=pyhd3eb1b0_0
  - jpeg=9d=h7f8727e_0
  - json5=0.9.6=pyhd3eb1b0_0
  - jsonschema=3.2.0=pyhd3eb1b0_2
  - jupyter=1.0.0=py39h06a4308_7
  - jupyter_client=6.1.12=pyhd3eb1b0_0
  - jupyter_console=6.4.0=pyhd3eb1b0_0
  - jupyter_core=4.8.1=py39h06a4308_0
  - jupyter_server=1.4.1=py39h06a4308_0
  - jupyterlab=3.2.1=pyhd3eb1b0_1
  - jupyterlab_pygments=0.1.2=py_0
  - jupyterlab_server=2.8.2=pyhd3eb1b0_0
  - jupyterlab_widgets=1.0.0=pyhd3eb1b0_1
  - jxrlib=1.1=h7b6447c_2
  - keyring=23.1.0=py39h06a4308_0
  - kiwisolver=1.3.1=py39h2531618_0
  - krb5=1.19.2=hac12032_0
  - lazy-object-proxy=1.6.0=py39h27cfd23_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.35.1=h7274673_9
  - lerc=3.0=h295c915_0
  - libaec=1.0.4=he6710b0_1
  - libarchive=3.4.2=h62408e4_0
  - libcurl=7.78.0=h0b77cf5_0
  - libdeflate=1.8=h7f8727e_5
  - libedit=3.1.20210910=h7f8727e_0
  - libev=4.33=h7f8727e_1
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.3.0=h5101ec6_17
  - libgfortran-ng=7.5.0=ha8ba4b0_17
  - libgfortran4=7.5.0=ha8ba4b0_17
  - libgomp=9.3.0=h5101ec6_17
  - liblief=0.10.1=h2531618_1
  - libllvm11=11.1.0=h3826bc1_0
  - libnghttp2=1.41.0=hf8bcb03_2
  - libpng=1.6.37=hbc83047_0
  - libsodium=1.0.18=h7b6447c_0
  - libspatialindex=1.9.3=h2531618_0
  - libssh2=1.9.0=h1ba5d50_1
  - libstdcxx-ng=9.3.0=hd4cf53a_17
  - libtiff=4.2.0=h85742a9_0
  - libtool=2.4.6=h7b6447c_1005
  - libuuid=1.0.3=h7f8727e_2
  - libuv=1.40.0=h7b6447c_0
  - libwebp=1.2.0=h89dd481_0
  - libwebp-base=1.2.0=h27cfd23_0
  - libxcb=1.14=h7b6447c_0
  - libxml2=2.9.12=h03d6c58_0
  - libxslt=1.1.34=hc22bd24_0
  - libzopfli=1.0.3=he6710b0_0
  - llvmlite=0.37.0=py39h295c915_1
  - locket=0.2.1=py39h06a4308_1
  - lxml=4.6.3=py39h9120a33_0
  - lz4-c=1.9.3=h295c915_1
  - lzo=2.10=h7b6447c_2
  - markupsafe=1.1.1=py39h27cfd23_0
  - matplotlib=3.4.3=py39h06a4308_0
  - matplotlib-base=3.4.3=py39hbbc1b5f_0
  - matplotlib-inline=0.1.2=pyhd3eb1b0_2
  - mccabe=0.6.1=py39h06a4308_1
  - mistune=0.8.4=py39h27cfd23_1000
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py39h7f8727e_0
  - mkl_fft=1.3.1=py39hd3c417c_0
  - mkl_random=1.2.2=py39h51133e4_0
  - mock=4.0.3=pyhd3eb1b0_0
  - more-itertools=8.10.0=pyhd3eb1b0_0
  - mpc=1.1.0=h10f8cd9_1
  - mpfr=4.0.2=hb69a4c5_1
  - mpi=1.0=mpich
  - mpich=3.3.2=hc856adb_0
  - mpmath=1.2.1=py39h06a4308_0
  - msgpack-python=1.0.2=py39hff7bd54_1
  - multipledispatch=0.6.0=py39h06a4308_0
  - munkres=1.1.4=py_0
  - mypy_extensions=0.4.3=py39h06a4308_0
  - navigator-updater=0.2.1=py39h06a4308_0
  - nbclassic=0.2.6=pyhd3eb1b0_0
  - nbclient=0.5.3=pyhd3eb1b0_0
  - nbconvert=6.1.0=py39h06a4308_0
  - nbformat=5.1.3=pyhd3eb1b0_0
  - ncurses=6.3=heee7806_1
  - nest-asyncio=1.5.1=pyhd3eb1b0_0
  - networkx=2.6.3=pyhd3eb1b0_0
  - nltk=3.6.5=pyhd3eb1b0_0
  - nose=1.3.7=pyhd3eb1b0_1006
  - notebook=6.4.5=py39h06a4308_0
  - numba=0.54.1=py39h51133e4_0
  - numexpr=2.7.3=py39h22e1b3c_1
  - numpy=1.20.3=py39hf144106_0
  - numpy-base=1.20.3=py39h74d4b33_0
  - numpydoc=1.1.0=pyhd3eb1b0_1
  - olefile=0.46=pyhd3eb1b0_0
  - openjpeg=2.4.0=h3ad879b_0
  - openpyxl=3.0.9=pyhd3eb1b0_0
  - openssl=1.1.1l=h7f8727e_0
  - packaging=21.0=pyhd3eb1b0_0
  - pandas=1.3.4=py39h8c16a72_0
  - pandocfilters=1.4.3=py39h06a4308_1
  - pango=1.45.3=hd140c19_0
  - parso=0.8.2=pyhd3eb1b0_0
  - partd=1.2.0=pyhd3eb1b0_0
  - patchelf=0.13=h295c915_0
  - path=16.0.0=py39h06a4308_0
  - path.py=12.5.0=hd3eb1b0_0
  - pathlib2=2.3.6=py39h06a4308_2
  - pathspec=0.7.0=py_0
  - patsy=0.5.2=py39h06a4308_0
  - pcre=8.45=h295c915_0
  - pep8=1.7.1=py39h06a4308_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=8.4.0=py39h5aabda8_0
  - pip=21.2.4=py39h06a4308_0
  - pixman=0.40.0=h7f8727e_1
  - pkginfo=1.7.1=py39h06a4308_0
  - pluggy=0.13.1=py39h06a4308_0
  - ply=3.11=py39h06a4308_0
  - poyo=0.5.0=pyhd3eb1b0_0
  - prometheus_client=0.11.0=pyhd3eb1b0_0
  - prompt-toolkit=3.0.20=pyhd3eb1b0_0
  - prompt_toolkit=3.0.20=hd3eb1b0_0
  - psutil=5.8.0=py39h27cfd23_1
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - py=1.10.0=pyhd3eb1b0_0
  - py-lief=0.10.1=py39h2531618_1
  - pycodestyle=2.7.0=pyhd3eb1b0_0
  - pycosat=0.6.3=py39h27cfd23_0
  - pycparser=2.20=py_2
  - pycurl=7.44.1=py39h8f2d780_1
  - pydocstyle=6.1.1=pyhd3eb1b0_0
  - pyerfa=2.0.0=py39h27cfd23_0
  - pyflakes=2.3.1=pyhd3eb1b0_0
  - pygments=2.10.0=pyhd3eb1b0_0
  - pyjwt=2.1.0=py39h06a4308_0
  - pylint=2.9.6=py39h06a4308_1
  - pyls-spyder=0.4.0=pyhd3eb1b0_0
  - pyodbc=4.0.31=py39h295c915_0
  - pyopenssl=21.0.0=pyhd3eb1b0_1
  - pyparsing=3.0.4=pyhd3eb1b0_0
  - pyqt=5.9.2=py39h2531618_6
  - pyrsistent=0.18.0=py39heee7806_0
  - pysocks=1.7.1=py39h06a4308_0
  - pytables=3.6.1=py39h77479fe_1
  - pytest=6.2.4=py39h06a4308_2
  - python=3.9.7=h12debd9_1
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-libarchive-c=2.9=pyhd3eb1b0_1
  - python-lsp-black=1.0.0=pyhd3eb1b0_0
  - python-lsp-jsonrpc=1.0.0=pyhd3eb1b0_0
  - python-lsp-server=1.2.4=pyhd3eb1b0_0
  - python-slugify=5.0.2=pyhd3eb1b0_0
  - pytz=2021.3=pyhd3eb1b0_0
  - pywavelets=1.1.1=py39h6323ea4_4
  - pyxdg=0.27=pyhd3eb1b0_0
  - pyyaml=6.0=py39h7f8727e_1
  - pyzmq=22.2.1=py39h295c915_1
  - qdarkstyle=3.0.2=pyhd3eb1b0_0
  - qstylizer=0.1.10=pyhd3eb1b0_0
  - qt=5.9.7=h5867ecd_1
  - qtawesome=1.0.2=pyhd3eb1b0_0
  - qtconsole=5.1.1=pyhd3eb1b0_0
  - qtpy=1.10.0=pyhd3eb1b0_0
  - readline=8.1=h27cfd23_0
  - regex=2021.8.3=py39h7f8727e_0
  - requests=2.26.0=pyhd3eb1b0_0
  - ripgrep=12.1.1=0
  - rope=0.19.0=pyhd3eb1b0_0
  - rtree=0.9.7=py39h06a4308_1
  - ruamel_yaml=0.15.100=py39h27cfd23_0
  - scikit-image=0.18.3=py39h51133e4_0
  - scikit-learn=0.24.2=py39ha9443f7_0
  - scikit-learn-intelex=2021.3.0=py39h06a4308_0
  - seaborn=0.11.2=pyhd3eb1b0_0
  - secretstorage=3.3.1=py39h06a4308_0
  - send2trash=1.8.0=pyhd3eb1b0_1
  - setuptools=58.0.4=py39h06a4308_0
  - simplegeneric=0.8.1=py39h06a4308_2
  - singledispatch=3.7.0=pyhd3eb1b0_1001
  - sip=4.19.13=py39h2531618_0
  - six=1.16.0=pyhd3eb1b0_0
  - snappy=1.1.8=he6710b0_0
  - sniffio=1.2.0=py39h06a4308_1
  - snowballstemmer=2.1.0=pyhd3eb1b0_0
  - sortedcollections=2.1.0=pyhd3eb1b0_0
  - sortedcontainers=2.4.0=pyhd3eb1b0_0
  - soupsieve=2.2.1=pyhd3eb1b0_0
  - sphinx=4.2.0=pyhd3eb1b0_1
  - sphinxcontrib=1.0=py39h06a4308_1
  - sphinxcontrib-applehelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-devhelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-htmlhelp=2.0.0=pyhd3eb1b0_0
  - sphinxcontrib-jsmath=1.0.1=pyhd3eb1b0_0
  - sphinxcontrib-qthelp=1.0.3=pyhd3eb1b0_0
  - sphinxcontrib-serializinghtml=1.1.5=pyhd3eb1b0_0
  - sphinxcontrib-websupport=1.2.4=py_0
  - spyder=5.1.5=py39h06a4308_1
  - spyder-kernels=2.1.3=py39h06a4308_0
  - sqlalchemy=1.4.22=py39h7f8727e_0
  - sqlite=3.36.0=hc218d9a_0
  - statsmodels=0.12.2=py39h27cfd23_0
  - sympy=1.9=py39h06a4308_0
  - tbb=2021.4.0=hd09550d_0
  - tbb4py=2021.4.0=py39hd09550d_0
  - tblib=1.7.0=pyhd3eb1b0_0
  - terminado=0.9.4=py39h06a4308_0
  - testpath=0.5.0=pyhd3eb1b0_0
  - text-unidecode=1.3=pyhd3eb1b0_0
  - textdistance=4.2.1=pyhd3eb1b0_0
  - threadpoolctl=2.2.0=pyh0d69192_0
  - three-merge=0.1.1=pyhd3eb1b0_0
  - tifffile=2021.7.2=pyhd3eb1b0_2
  - tinycss=0.4=pyhd3eb1b0_1002
  - tk=8.6.11=h1ccaba5_0
  - toml=0.10.2=pyhd3eb1b0_0
  - toolz=0.11.1=pyhd3eb1b0_0
  - tornado=6.1=py39h27cfd23_0
  - traitlets=5.1.0=pyhd3eb1b0_0
  - typed-ast=1.4.3=py39h7f8727e_1
  - tzdata=2021e=hda174b7_0
  - ujson=4.0.2=py39h2531618_0
  - unicodecsv=0.14.1=py39h06a4308_0
  - unidecode=1.2.0=pyhd3eb1b0_0
  - unixodbc=2.3.9=h7b6447c_0
  - urllib3=1.26.7=pyhd3eb1b0_0
  - watchdog=2.1.3=py39h06a4308_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - webencodings=0.5.1=py39h06a4308_1
  - werkzeug=2.0.2=pyhd3eb1b0_0
  - wheel=0.37.0=pyhd3eb1b0_1
  - whichcraft=0.6.1=pyhd3eb1b0_0
  - widgetsnbextension=3.5.1=py39h06a4308_0
  - wrapt=1.12.1=py39he8ac12f_1
  - wurlitzer=2.1.1=py39h06a4308_0
  - xlrd=2.0.1=pyhd3eb1b0_0
  - xlsxwriter=3.0.1=pyhd3eb1b0_0
  - xlwt=1.3.0=py39h06a4308_0
  - xmltodict=0.12.0=pyhd3eb1b0_0
  - xz=5.2.5=h7b6447c_0
  - yaml=0.2.5=h7b6447c_0
  - yapf=0.31.0=pyhd3eb1b0_0
  - zeromq=4.3.4=h2531618_0
  - zfp=0.5.5=h2531618_6
  - zict=2.0.0=pyhd3eb1b0_0
  - zipp=3.6.0=pyhd3eb1b0_0
  - zlib=1.2.11=h7b6447c_3
  - zope=1.0=py39h06a4308_1
  - zope.event=4.5.0=py39h06a4308_0
  - zope.interface=5.4.0=py39h7f8727e_0
  - zstd=1.4.9=haebb681_0
  - pip:
    - absl-py==1.0.0
    - accelerate==0.16.0
    - addict==2.4.0
    - aiohttp==3.8.3
    - aiosignal==1.3.1
    - aliyun-python-sdk-core==2.13.36
    - aliyun-python-sdk-kms==2.16.0
    - antlr4-python3-runtime==4.9.3
    - apex==0.1
    - argparse-hparams==0.0.1.dev20220103160002
    - astor==0.8.1
    - astunparse==1.6.3
    - async-timeout==4.0.2
    - audioread==3.0.0
    - bce-python-sdk==0.8.74
    - blessed==1.19.1
    - boto3==1.21.46
    - botocore==1.24.46
    - cachetools==4.2.4
    - colorlog==6.7.0
    - configparser==5.2.0
    - crcmod==1.7
    - ctcdecode==1.0.3
    - cupy==12.1.0
    - datasets==2.8.0
    - diffusers==0.12.1
    - dill==0.3.4
    - docker-pycreds==0.4.0
    - easydict==1.10
    - einops==0.3.0
    - fastrlock==0.8.1
    - flask-babel==2.0.0
    - flatbuffers==2.0
    - frozenlist==1.3.3
    - fsspec==2022.11.0
    - ftfy==6.0.3
    - gast==0.4.0
    - gensim==4.1.2
    - gitdb==4.0.9
    - gitpython==3.1.26
    - google-auth==2.3.3
    - google-auth-oauthlib==0.4.6
    - google-pasta==0.2.0
    - gpustat==1.0.0
    - grpcio==1.51.1
    - huggingface-hub==0.12.0
    - jieba==0.42.1
    - jmespath==0.10.0
    - json-tricks==3.15.5
    - jsonplus==0.8.0
    - keras==2.7.0
    - keras-preprocessing==1.1.2
    - levenshtein==0.16.0
    - libclang==13.0.0
    - librosa==0.9.2
    - lmdb==1.2.1
    - markdown==3.3.6
    - mmcv==1.3.11
    - modelscope==1.3.0
    - multidict==6.0.2
    - multiprocess==*********
    - natsort==8.0.2
    - natten==0.14.6+torch1101cu113
    - nlg-eval==2.3
    - nvidia-ml-py==11.495.46
    - oauthlib==3.1.1
    - omegaconf==2.3.0
    - opencv-python==********
    - opt-einsum==3.3.0
    - oss2==2.16.0
    - paddle-bfloat==0.1.7
    - paddle2onnx==1.0.2
    - paddlefsl==1.1.0
    - paddlenlp==2.4.2
    - paddlepaddle-gpu==2.3.2.post112
    - pathtools==0.1.2
    - pooch==1.6.0
    - portalocker==2.3.2
    - promise==2.3
    - protobuf==3.20.1
    - pyarrow==10.0.0
    - pyasn1==0.4.8
    - pyasn1-modules==0.2.8
    - pycocoevalcap==1.2
    - pycocotools==2.0.6
    - pycryptodome==3.15.0
    - pydeprecate==0.3.2
    - pytorch-lightning==1.7.7
    - rapidfuzz==1.8.3
    - requests-oauthlib==1.3.0
    - resampy==0.4.2
    - responses==0.18.0
    - rouge-score==0.0.4
    - rsa==4.8
    - s3transfer==0.5.2
    - sacrebleu==2.3.1
    - sacremoses==0.0.53
    - scipy==1.7.3
    - sentence-transformers==2.2.2
    - sentencepiece==0.1.97
    - sentry-sdk==1.5.4
    - seqeval==1.2.2
    - shortuuid==1.0.8
    - simplejson==3.18.3
    - smart-open==5.2.1
    - smmap==5.0.0
    - soundfile==0.12.0
    - subprocess32==3.5.4
    - tabulate==0.9.0
    - taming-transformers-rom1504==0.0.6
    - tb-nightly==2.8.0a20211225
    - tensorboard==2.12.0
    - tensorboard-data-server==0.7.0
    - tensorboard-plugin-wit==1.8.0
    - tensorboardx==2.4.1
    - tensorflow==2.7.0
    - tensorflow-addons==0.15.0
    - tensorflow-estimator==2.7.0
    - tensorflow-io-gcs-filesystem==0.23.1
    - termcolor==1.1.0
    - theano==1.0.5
    - thop==0.0.31-2005241907
    - timm==0.4.12
    - tokenizers==0.12.1
    - torch==1.10.1+cu113
    - torch-summary==1.4.5
    - torchcam==0.3.2
    - torchmetrics==0.11.1
    - torchtext==0.11.1
    - torchvision==0.11.2+cu113
    - torchzq==1.1.0.dev20220109163729
    - tqdm==4.64.1
    - transformers==4.18.0
    - typeguard==2.7.0
    - typing-extensions==4.5.0
    - unicodedata2==15.0.0
    - visualdl==2.4.1
    - wandb==0.12.9
    - xdg==5.1.1
    - xxhash==3.1.0
    - yacs==0.1.8
    - yarl==1.8.1
    - yaspin==2.1.0
    - zhconv==1.4.3
prefix: /home/<USER>/anaconda3/envs/old_slt
