"""
生成论文所需的动态时空注意力可视化图表
"""
import os
import numpy as np
import torch
import cv2
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from collections import OrderedDict
import glob
from utils import video_augmentation
from slr_network import SLRModel
import utils
from modules.attention import DynamicTemporalAttention
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，适合服务器环境

# 配置参数
gpu_id = 0  # 使用的GPU ID
dataset = 'phoenix2014-T'  # 支持 [phoenix2014, phoenix2014-T, CSL-Daily]
prefix = './dataset/PHOENIX-2014-T-release-v3/PHOENIX-2014-T'
dict_path = f'./preprocess/{dataset}/gloss_dict.npy'
model_weights = './pretrain_model/dev_18.90_PHOENIX14-T.pt'  # 替换为您的模型路径
select_id = 0  # 选择要可视化的视频索引
output_dir = './paper_figures'  # 输出目录

# 创建输出目录
os.makedirs(output_dir, exist_ok=True)

def load_data():
    """加载数据和预处理"""
    gloss_dict = np.load(dict_path, allow_pickle=True).item()
    inputs_list = np.load(f"./preprocess/{dataset}/dev_info.npy", allow_pickle=True).item()
    name = inputs_list[select_id]['fileid']
    print(f'Generating visualizations for {name}')

    # 加载图像
    img_folder = os.path.join(prefix, "features/fullFrame-256x256px/" + inputs_list[select_id]['folder']) if 'phoenix' in dataset else os.path.join(prefix, inputs_list[select_id]['folder'])
    img_list = sorted(glob.glob(img_folder))
    img_list = [cv2.cvtColor(cv2.imread(img_path), cv2.COLOR_BGR2RGB) for img_path in img_list]

    # 处理标签
    label_list = []
    for phase in inputs_list[select_id]['label'].split(" "):
        if phase == '':
            continue
        if phase in gloss_dict.keys():
            label_list.append(gloss_dict[phase][0])

    # 数据转换
    transform = video_augmentation.Compose([
        video_augmentation.CenterCrop(224),
        video_augmentation.Resize(1.0),
        video_augmentation.ToTensor(),
    ])
    vid, label = transform(img_list, label_list, None)
    vid = vid.float() / 127.5 - 1
    vid = vid.unsqueeze(0)

    # 填充处理
    left_pad = 0
    last_stride = 1
    total_stride = 1
    kernel_sizes = ['K5', "P2", 'K5', "P2"]
    for layer_idx, ks in enumerate(kernel_sizes):
        if ks[0] == 'K':
            left_pad = left_pad * last_stride
            left_pad += int((int(ks[1])-1)/2)
        elif ks[0] == 'P':
            last_stride = int(ks[1])
            total_stride = total_stride * last_stride

    max_len = vid.size(1)
    video_length = torch.LongTensor([
        int(np.ceil(max_len / total_stride)) * total_stride + 2 * left_pad
    ])
    right_pad = int(np.ceil(max_len / total_stride)) * total_stride - max_len + left_pad
    max_len = max_len + left_pad + right_pad
    vid = torch.cat(
        (
            vid[0,0][None].expand(left_pad, -1, -1, -1),
            vid[0],
            vid[0,-1][None].expand(max_len - vid.size(1) - left_pad, -1, -1, -1),
        ), dim=0).unsqueeze(0)

    return vid, video_length, label_list, gloss_dict, img_list

def init_model(gloss_dict):
    """初始化模型"""
    device = utils.GpuDataParallel()
    device.set_device(gpu_id)

    # 创建带有注意力参数的模型
    attention_params = {
        'max_window_size': 11,
        'kernel_sizes': [3, 5, 7],
        'reduction_ratio': 16
    }

    model = SLRModel(
        num_classes=len(gloss_dict)+1,
        c2d_type='resnet18',
        conv_type=2,
        use_bn=1,
        gloss_dict=gloss_dict,
        loss_weights={'ConvCTC': 1.0, 'SeqCTC': 1.0, 'Dist': 25.0},
        attention_params=attention_params
    )

    # 加载预训练权重
    state_dict = torch.load(model_weights)['model_state_dict']
    state_dict = OrderedDict([(k.replace('.module', ''), v) for k, v in state_dict.items()])
    model.load_state_dict(state_dict, strict=True)
    model = model.to(device.output_device)
    model.eval()  # 设置为评估模式

    return model, device

def generate_attention_mechanism_figure():
    """生成动态时空注意力机制的示意图"""
    plt.figure(figsize=(12, 8))

    # 创建一个3x3的网格
    gs = plt.GridSpec(3, 3, figure=plt.gcf())

    # 1. 输入特征表示
    ax1 = plt.subplot(gs[0, 0])
    ax1.imshow(np.random.rand(10, 20), cmap='viridis')
    ax1.set_title('Input Features')
    ax1.set_xticks([])
    ax1.set_yticks([])

    # 2. 多尺度卷积核
    ax2 = plt.subplot(gs[0, 1])
    kernel_sizes = [3, 5, 7]
    for i, k in enumerate(kernel_sizes):
        rect = plt.Rectangle((i*3, 0), 2, k, fc='skyblue', ec='blue', alpha=0.7)
        ax2.add_patch(rect)
    ax2.set_xlim(0, 10)
    ax2.set_ylim(0, 10)
    ax2.set_title('Multi-scale Kernels')
    ax2.set_xticks([])
    ax2.set_yticks([])

    # 3. 动态权重预测
    ax3 = plt.subplot(gs[0, 2])
    t = np.linspace(0, 10, 100)
    for i, k in enumerate(kernel_sizes):
        ax3.plot(t, 0.3 + 0.1 * np.sin(t + i), label=f'Kernel {k}')
    ax3.set_title('Dynamic Weight Prediction')
    ax3.legend(fontsize='small')

    # 4. 时间门控机制
    ax4 = plt.subplot(gs[1, 0:2])
    t = np.linspace(0, 10, 100)
    gate = 0.5 + 0.4 * np.sin(t)
    ax4.plot(t, gate)
    ax4.set_title('Temporal Gating Mechanism')
    ax4.set_xlabel('Time Steps')
    ax4.set_ylabel('Gate Value')

    # 5. 特征融合
    ax5 = plt.subplot(gs[1, 2])
    ax5.imshow(np.random.rand(10, 20), cmap='plasma')
    ax5.set_title('Feature Fusion')
    ax5.set_xticks([])
    ax5.set_yticks([])

    # 6. 整体流程图
    ax6 = plt.subplot(gs[2, :])
    # 创建一个简单的流程图
    nodes = ['Input', 'Multi-scale Conv', 'Dynamic Weights', 'Feature Fusion', 'Temporal Gate', 'Output']
    node_positions = np.array([[0, 0], [1, 0], [2, 1], [3, 0], [4, 1], [5, 0]])

    # 绘制节点
    for i, (node, pos) in enumerate(zip(nodes, node_positions)):
        circle = plt.Circle((pos[0], pos[1]), 0.2, fc='lightblue', ec='blue')
        ax6.add_patch(circle)
        ax6.text(pos[0], pos[1], node, ha='center', va='center', fontsize=9)

    # 绘制连接线
    arrows = [(0, 1), (1, 3), (2, 3), (3, 5), (4, 5)]
    for start, end in arrows:
        start_pos = node_positions[start]
        end_pos = node_positions[end]
        ax6.arrow(start_pos[0] + 0.2, start_pos[1],
                 end_pos[0] - start_pos[0] - 0.4, end_pos[1] - start_pos[1],
                 head_width=0.05, head_length=0.1, fc='black', ec='black')

    ax6.set_xlim(-0.5, 5.5)
    ax6.set_ylim(-0.5, 1.5)
    ax6.set_title('Dynamic Spatiotemporal Attention Flow')
    ax6.set_xticks([])
    ax6.set_yticks([])

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'attention_mechanism.png'), dpi=300)
    plt.close()

def generate_attention_visualization(model, vid, vid_lgt, device, original_frames):
    """生成注意力可视化"""
    # 收集注意力模块的激活
    attention_modules = []
    for name, module in model.named_modules():
        if isinstance(module, DynamicTemporalAttention):
            attention_modules.append((name, module))

    # 前向传播，保存中间结果
    with torch.no_grad():
        vid = device.data_to_device(vid)
        vid_lgt = device.data_to_device(vid_lgt)

        # 修改前向传播，启用中间结果保存
        def forward_with_intermediates(module, *args, **kwargs):
            return module.forward(*args, save_intermediates=True, **kwargs)

        # 临时替换前向传播函数
        original_forwards = {}
        for name, module in attention_modules:
            original_forwards[name] = module.forward
            # 创建一个闭包来保存模块引用
            def create_forward_wrapper(mod):
                return lambda *args, **kwargs: forward_with_intermediates(mod, *args, **kwargs)
            module.forward = create_forward_wrapper(module)

        # 执行前向传播
        ret_dict = model(vid, vid_lgt)

        # 恢复原始前向传播函数
        for name, module in attention_modules:
            module.forward = original_forwards[name]

    # 为每个注意力模块生成可视化
    for name, module in attention_modules:
        # 使用模块内置的可视化方法
        module.visualize_attention(output_dir=os.path.join(output_dir, name.replace('.', '_')),
                                  prefix='')

        # 生成特征与原始帧的叠加图
        if 'input' in module.intermediate_results and len(original_frames) > 0:
            # 获取时间门控
            if 'temporal_gate' in module.intermediate_results:
                gate = module.intermediate_results['temporal_gate'].cpu().numpy()

                # 为几个关键帧创建叠加图
                for t in range(min(5, gate.shape[2])):
                    if t < len(original_frames):
                        # 获取原始帧
                        orig_frame = original_frames[t]
                        orig_frame = cv2.resize(orig_frame, (224, 224))

                        # 获取门控热力图
                        gate_map = np.mean(gate[0, :, t, :, :], axis=0)
                        gate_map = cv2.resize(gate_map, (224, 224))
                        gate_map = (gate_map - np.min(gate_map)) / (np.max(gate_map) - np.min(gate_map) + 1e-8)
                        heatmap = cv2.applyColorMap((gate_map * 255).astype(np.uint8), cv2.COLORMAP_JET)

                        # 创建叠加图像
                        alpha = 0.6
                        overlay = cv2.addWeighted(orig_frame, 1-alpha, heatmap, alpha, 0)

                        # 保存结果
                        cv2.imwrite(os.path.join(output_dir, f"{name.replace('.', '_')}_overlay_t{t}.jpg"),
                                   cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR))

def main():
    """主函数"""
    # 生成注意力机制示意图
    generate_attention_mechanism_figure()

    # 加载数据
    vid, video_length, label_list, gloss_dict, original_frames = load_data()

    # 初始化模型
    model, device = init_model(gloss_dict)

    # 生成注意力可视化
    generate_attention_visualization(model, vid, video_length, device, original_frames)

    print(f"所有可视化已保存到 {output_dir}")

if __name__ == "__main__":
    main()
