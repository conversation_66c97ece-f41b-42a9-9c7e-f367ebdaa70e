#!/usr/bin/env python3
"""
Example usage of the CAM generation tools
"""

import os
import subprocess
import sys

def run_command(cmd, description):
    """Run a command and print results"""
    print(f"\n{'='*60}")
    print(f"EXAMPLE: {description}")
    print('='*60)
    print(f"Command: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ Success!")
        if result.stdout:
            print("Output:")
            print(result.stdout)
    except subprocess.CalledProcessError as e:
        print("✗ Failed!")
        print(f"Error: {e.stderr}")
        return False
    except FileNotFoundError:
        print("✗ Script not found! Make sure you're in the correct directory.")
        return False
    
    return True

def main():
    print("=== CAM Generation Examples ===")
    print("This script demonstrates various ways to use the CAM generation tools.")
    print("Make sure you have set up your model checkpoint and data paths first!")
    
    # Example 1: Test setup
    print("\n" + "="*60)
    print("STEP 1: Testing Setup")
    print("="*60)
    
    cmd = [sys.executable, 'test_cam_setup.py']
    if not run_command(cmd, "Test CAM setup and dependencies"):
        print("Setup test failed. Please fix the issues before proceeding.")
        return
    
    # Example 2: Quick start
    print("\n" + "="*60)
    print("STEP 2: Quick Start (Test Only)")
    print("="*60)
    
    cmd = [sys.executable, 'quick_start_cam.py', '--test_only']
    if not run_command(cmd, "Quick start test (no CAM generation)"):
        print("Quick start test failed. Please check your data paths.")
        return
    
    # Ask user if they want to continue with actual CAM generation
    print("\n" + "="*60)
    print("READY FOR CAM GENERATION")
    print("="*60)
    
    response = input("Do you want to proceed with actual CAM generation? (y/n): ").lower()
    if response != 'y':
        print("Stopping here. You can run the examples manually when ready.")
        return
    
    # Example 3: Single video CAM
    cmd = [sys.executable, 'quick_start_cam.py', '--video_id', '0']
    run_command(cmd, "Generate CAM for single video (ID: 0)")
    
    # Example 4: Basic CAM with custom parameters
    cmd = [
        sys.executable, 'generate_cam.py',
        '--select_id', '1',
        '--target_layers', 'conv2d.corr2.conv_back', 'conv2d.corr1.conv_back',
        '--alpha', '0.6',
        '--save_original',
        '--output_dir', './example_CAM_results'
    ]
    run_command(cmd, "Generate CAM with multiple layers and custom alpha")
    
    # Example 5: Configuration-based generation
    if os.path.exists('cam_config.yaml'):
        cmd = [
            sys.executable, 'advanced_cam_generator.py',
            '--config', 'cam_config.yaml',
            '--experiment', 'correlation_only',
            '--video_ids', '0', '1', '2',
            '--create_comparisons'
        ]
        run_command(cmd, "Advanced CAM generation with configuration file")
    else:
        print("\nSkipping advanced example (cam_config.yaml not found)")
    
    # Example 6: Batch processing (small batch)
    cmd = [
        sys.executable, 'batch_generate_cam.py',
        '--start_id', '0',
        '--end_id', '2',
        '--output_dir', './example_batch_results',
        '--target_layers', 'conv2d.corr2.conv_back'
    ]
    run_command(cmd, "Batch process 3 videos")
    
    # Example 7: Create comparisons
    if os.path.exists('./example_CAM_results'):
        cmd = [
            sys.executable, 'visualize_cam_comparison.py',
            '--input_dir', './example_CAM_results',
            '--output_dir', './example_comparisons',
            '--comparison_type', 'layers',
            '--max_frames', '6'
        ]
        run_command(cmd, "Create layer comparison visualizations")
    
    print("\n" + "="*60)
    print("EXAMPLES COMPLETED")
    print("="*60)
    
    print("\nGenerated outputs:")
    output_dirs = [
        './quick_CAM_results',
        './example_CAM_results', 
        './example_batch_results',
        './example_comparisons'
    ]
    
    for output_dir in output_dirs:
        if os.path.exists(output_dir):
            print(f"✓ {output_dir}")
        else:
            print(f"- {output_dir} (not created)")
    
    print("\nNext steps:")
    print("1. Explore the generated CAM visualizations")
    print("2. Modify cam_config.yaml for your specific needs")
    print("3. Use batch_generate_cam.py for processing many videos")
    print("4. Create custom comparison visualizations")
    
    print("\nFor more options, run:")
    print("  python generate_cam.py --help")
    print("  python batch_generate_cam.py --help")
    print("  python advanced_cam_generator.py --help")

if __name__ == "__main__":
    main()
