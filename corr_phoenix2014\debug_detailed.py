#!/usr/bin/env python3
"""
Detailed debug script to find the exact issue
"""

import sys
import os
import inspect

def debug_resnet_class():
    """Debug ResNet class in detail"""
    print("=== Debugging ResNet Class ===")
    
    try:
        # Import the module
        from modules import resnet
        print(f"✓ Imported resnet module from: {resnet.__file__}")
        
        # Check ResNet class
        ResNet = resnet.ResNet
        print(f"✓ ResNet class: {ResNet}")
        print(f"✓ ResNet module: {ResNet.__module__}")
        print(f"✓ ResNet file: {inspect.getfile(ResNet)}")
        
        # Get the actual signature
        sig = inspect.signature(ResNet.__init__)
        print(f"✓ ResNet.__init__ signature: {sig}")
        
        # List all parameters
        params = list(sig.parameters.keys())
        print(f"✓ Parameters: {params}")
        
        # Check if attention_params is there
        if 'attention_params' in params:
            print("✓ attention_params found in signature")
        else:
            print("✗ attention_params NOT found in signature")
            print("This is the problem!")
        
        # Get the source code
        try:
            source = inspect.getsource(ResNet.__init__)
            print(f"✓ Source code first few lines:")
            lines = source.split('\n')[:10]
            for i, line in enumerate(lines):
                print(f"  {i+1}: {line}")
        except Exception as e:
            print(f"✗ Could not get source: {e}")
        
        return 'attention_params' in params
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def debug_imports():
    """Debug import conflicts"""
    print("\n=== Debugging Imports ===")
    
    # Check what's in sys.modules
    resnet_modules = [k for k in sys.modules.keys() if 'resnet' in k.lower()]
    print(f"ResNet-related modules in sys.modules: {resnet_modules}")
    
    # Check if there are multiple ResNet classes
    try:
        from modules import resnet as local_resnet
        print(f"Local resnet module: {local_resnet}")
        
        # Check if torchvision resnet is imported
        try:
            import torchvision.models.resnet as torch_resnet
            print(f"Torchvision resnet module: {torch_resnet}")
            
            # Compare ResNet classes
            local_ResNet = local_resnet.ResNet
            torch_ResNet = torch_resnet.ResNet
            
            print(f"Local ResNet: {local_ResNet}")
            print(f"Torch ResNet: {torch_ResNet}")
            
            if local_ResNet == torch_ResNet:
                print("⚠ WARNING: Local and torch ResNet are the same!")
                print("This suggests import conflict!")
            else:
                print("✓ Local and torch ResNet are different")
                
        except ImportError:
            print("✓ No torchvision resnet conflict")
        
    except Exception as e:
        print(f"✗ Error checking imports: {e}")


def debug_file_content():
    """Debug actual file content"""
    print("\n=== Debugging File Content ===")
    
    try:
        # Read the actual file
        with open('modules/resnet.py', 'r') as f:
            content = f.read()
        
        # Find ResNet class definition
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'class ResNet' in line:
                print(f"Found ResNet class at line {i+1}: {line}")
                # Print next few lines
                for j in range(1, 10):
                    if i+j < len(lines):
                        print(f"  {i+j+1}: {lines[i+j]}")
                break
        
        # Search for __init__ method
        for i, line in enumerate(lines):
            if 'def __init__(self, block, layers' in line:
                print(f"Found __init__ at line {i+1}: {line}")
                break
        
    except Exception as e:
        print(f"✗ Error reading file: {e}")


def test_direct_creation():
    """Test direct ResNet creation"""
    print("\n=== Testing Direct Creation ===")
    
    try:
        # Try to create ResNet without any imports
        exec("""
import sys
import os
sys.path.insert(0, '.')

# Import required modules
import torch
import torch.nn as nn
from modules.attention import DynamicTemporalAttention

# Define BasicBlock locally
class BasicBlock(nn.Module):
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, downsample=None):
        super(BasicBlock, self).__init__()
        self.conv1 = nn.Conv3d(inplanes, planes, kernel_size=(1,3,3), stride=(1,stride,stride),
                               padding=(0,1,1), bias=False)
        self.bn1 = nn.BatchNorm3d(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv3d(planes, planes, kernel_size=(1,3,3), stride=1,
                               padding=(0,1,1), bias=False)
        self.bn2 = nn.BatchNorm3d(planes)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        out = self.conv2(out)
        out = self.bn2(out)
        if self.downsample is not None:
            residual = self.downsample(x)
        out += residual
        out = self.relu(out)
        return out

# Define ResNet locally with attention_params
class TestResNet(nn.Module):
    def __init__(self, block, layers, num_classes=1000, attention_params=None):
        self.inplanes = 64
        super(TestResNet, self).__init__()
        print(f"TestResNet created with attention_params: {attention_params}")

# Test creation
test_model = TestResNet(BasicBlock, [2, 2, 2, 2], attention_params={'test': True})
print("✓ Direct TestResNet creation successful")
""")
        
    except Exception as e:
        print(f"✗ Direct creation failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    print("=== Detailed ResNet Debug ===\n")
    
    # Run all debug functions
    debug_functions = [
        debug_resnet_class,
        debug_imports,
        debug_file_content,
        test_direct_creation
    ]
    
    for func in debug_functions:
        try:
            func()
        except Exception as e:
            print(f"Error in {func.__name__}: {e}")
        print()


if __name__ == "__main__":
    main()
