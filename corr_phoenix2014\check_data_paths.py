#!/usr/bin/env python3
"""
Check and fix data paths for CAM generation
"""

import os
import glob
import numpy as np
import argparse


def check_data_structure(prefix_path, dataset='phoenix2014'):
    """Check the data structure and find correct paths"""
    print(f"Checking data structure at: {prefix_path}")
    
    if not os.path.exists(prefix_path):
        print(f"✗ Prefix path does not exist: {prefix_path}")
        return None
    
    # Check for different possible structures
    possible_image_paths = [
        "features/fullFrame-256x256px",
        "fullFrame-256x256px", 
        "images",
        "frames",
        "."
    ]
    
    found_paths = []
    
    for img_path in possible_image_paths:
        full_path = os.path.join(prefix_path, img_path)
        if os.path.exists(full_path):
            print(f"✓ Found: {full_path}")
            found_paths.append(full_path)
            
            # Check subdirectories
            subdirs = [d for d in os.listdir(full_path) if os.path.isdir(os.path.join(full_path, d))]
            print(f"  Subdirectories: {subdirs[:5]}{'...' if len(subdirs) > 5 else ''}")
    
    return found_paths


def check_dev_info(dataset='phoenix2014'):
    """Check dev_info.npy file"""
    dev_info_path = f"./preprocess/{dataset}/dev_info.npy"
    
    if not os.path.exists(dev_info_path):
        print(f"✗ Dev info file not found: {dev_info_path}")
        return None
    
    try:
        data = np.load(dev_info_path, allow_pickle=True)

        # Handle different formats
        if isinstance(data, np.ndarray):
            if data.shape == ():  # scalar array containing dict
                inputs_list = data.item()
            else:  # array of items
                inputs_list = data.tolist()
        else:
            inputs_list = data

        print(f"✓ Loaded dev_info.npy with {len(inputs_list)} entries")

        # Show first few entries safely
        try:
            for i in range(min(3, len(inputs_list))):
                entry = inputs_list[i]
                print(f"  Entry {i}: {entry}")
        except Exception as e:
            print(f"  Could not display entries: {e}")

        return inputs_list
    except Exception as e:
        print(f"✗ Error loading dev_info.npy: {e}")
        import traceback
        traceback.print_exc()
        return None


def find_video_images(prefix_path, video_info):
    """Find images for a specific video"""
    print(f"\nSearching for video: {video_info['fileid']}")
    print(f"Video info: {video_info}")
    
    # Try different path combinations
    possible_patterns = [
        # Original pattern
        os.path.join(prefix_path, "features/fullFrame-256x256px", video_info['folder'], "*.png"),
        os.path.join(prefix_path, "features/fullFrame-256x256px", video_info['folder'], "*.jpg"),
        
        # Without features subdirectory
        os.path.join(prefix_path, "fullFrame-256x256px", video_info['folder'], "*.png"),
        os.path.join(prefix_path, "fullFrame-256x256px", video_info['folder'], "*.jpg"),
        
        # Direct folder access
        os.path.join(prefix_path, video_info['folder'], "*.png"),
        os.path.join(prefix_path, video_info['folder'], "*.jpg"),
        
        # With different subfolder structures
        os.path.join(prefix_path, "features/fullFrame-256x256px", video_info['folder'], "*", "*.png"),
        os.path.join(prefix_path, "features/fullFrame-256x256px", video_info['folder'], "*", "*.jpg"),
    ]
    
    for pattern in possible_patterns:
        files = glob.glob(pattern)
        if files:
            print(f"✓ Found {len(files)} images with pattern: {pattern}")
            return sorted(files)
        else:
            print(f"✗ No images found with pattern: {pattern}")
    
    return None


def suggest_fixes(prefix_path, inputs_list):
    """Suggest fixes for data path issues"""
    print("\n" + "="*60)
    print("SUGGESTED FIXES")
    print("="*60)
    
    # Check if any videos have images
    working_videos = []
    
    for i, video_info in enumerate(inputs_list[:10]):  # Check first 10 videos
        images = find_video_images(prefix_path, video_info)
        if images:
            working_videos.append(i)
    
    if working_videos:
        print(f"✓ Found working videos: {working_videos}")
        print(f"You can try these video IDs: {', '.join(map(str, working_videos))}")
        
        # Show command examples
        print(f"\nExample commands:")
        for vid_id in working_videos[:3]:
            print(f"  python generate_cam_dta.py --select_id {vid_id}")
    else:
        print("✗ No working videos found")
        print("\nPossible solutions:")
        print("1. Check if the dataset path is correct")
        print("2. Verify the dataset is properly extracted")
        print("3. Update the --prefix argument to point to the correct location")
        print("4. Use the test script with dummy data: python test_cam_with_checkpoint.py")


def main():
    parser = argparse.ArgumentParser(description='Check data paths for CAM generation')
    parser.add_argument('--prefix', type=str, 
                        default='./dataset/phoenix2014/phoenix-2014-multisigner',
                        help='Dataset prefix path')
    parser.add_argument('--dataset', type=str, default='phoenix2014',
                        help='Dataset name')
    parser.add_argument('--check_video_id', type=int, default=0,
                        help='Specific video ID to check')
    
    args = parser.parse_args()
    
    print("=== Data Path Checker ===\n")
    
    # Check basic structure
    found_paths = check_data_structure(args.prefix, args.dataset)
    
    # Check dev_info
    inputs_list = check_dev_info(args.dataset)
    
    if inputs_list is None:
        print("\n❌ Cannot proceed without dev_info.npy")
        return
    
    # Check specific video
    if args.check_video_id < len(inputs_list):
        video_info = inputs_list[args.check_video_id]
        images = find_video_images(args.prefix, video_info)
        
        if images:
            print(f"\n✅ Video {args.check_video_id} has {len(images)} images")
            print("You can run CAM generation with:")
            print(f"  python generate_cam_dta.py --select_id {args.check_video_id}")
        else:
            print(f"\n❌ Video {args.check_video_id} has no images")
    
    # Suggest fixes
    suggest_fixes(args.prefix, inputs_list)
    
    print(f"\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    if found_paths and inputs_list:
        print("✓ Basic data structure exists")
        print("✓ Dev info file loaded successfully")
        print("\nNext steps:")
        print("1. Try the suggested video IDs above")
        print("2. Or use: python test_cam_with_checkpoint.py (works without real data)")
    else:
        print("❌ Data setup issues detected")
        print("Please check the dataset installation")


if __name__ == "__main__":
    main()
