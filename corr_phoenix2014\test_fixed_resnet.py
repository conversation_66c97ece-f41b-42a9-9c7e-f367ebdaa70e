#!/usr/bin/env python3
"""
Test the fixed ResNet implementation
"""

import sys
import os

def test_resnet_creation():
    """Test ResNet creation with the fixes"""
    print("Testing fixed ResNet creation...")
    
    try:
        # Clear any cached modules
        modules_to_clear = [
            'modules.resnet',
            'modules.attention', 
            'slr_network'
        ]
        
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
                print(f"Cleared cached module: {module}")
        
        # Import fresh modules
        from modules import resnet
        from modules.attention import DynamicTemporalAttention
        from slr_network import SLRModel
        
        print("✓ Modules imported successfully")
        
        # Test 1: ResNet class directly
        print("\n1. Testing ResNet class directly...")
        attention_params = {
            'max_window_size': 9,
            'kernel_sizes': [3, 5, 7],
            'reduction_ratio': 16
        }
        
        model = resnet.ResNet(resnet.BasicBlock, [2, 2, 2, 2], attention_params=attention_params)
        print("✓ ResNet class created successfully")
        
        # Check DTA modules
        if hasattr(model, 'corr1') and hasattr(model, 'corr2'):
            print("✓ DynamicTemporalAttention modules found")
        else:
            print("✗ DynamicTemporalAttention modules not found")
        
        # Test 2: resnet18 function
        print("\n2. Testing resnet18 function...")
        model2 = resnet.resnet18(attention_params=attention_params)
        print("✓ resnet18 function successful")
        
        # Test 3: SLRModel creation
        print("\n3. Testing SLRModel creation...")
        slr_model = SLRModel(
            num_classes=1000,
            c2d_type='resnet18',
            conv_type=2,
            use_bn=1,
            attention_params=attention_params
        )
        print("✓ SLRModel created successfully")
        
        # Test 4: Check target layers
        print("\n4. Testing target layers...")
        target_layers = [
            'conv2d.corr2.fusion.0',
            'conv2d.corr2.temporal_gate.0',
            'conv2d.corr1.fusion.0',
            'conv2d.corr1.temporal_gate.0',
            'conv2d.layer4.1.conv2',
            'conv2d.layer3.1.conv2'
        ]
        
        found_layers = []
        missing_layers = []
        
        for layer_name in target_layers:
            layer = slr_model
            try:
                for attr in layer_name.split('.'):
                    layer = getattr(layer, attr)
                print(f"✓ Layer {layer_name} found")
                found_layers.append(layer_name)
            except AttributeError:
                print(f"✗ Layer {layer_name} not found")
                missing_layers.append(layer_name)
        
        print(f"\nSummary: {len(found_layers)} found, {len(missing_layers)} missing")
        
        if len(found_layers) >= 4:  # At least some DTA layers should be found
            print("✓ Sufficient target layers found for CAM generation")
            return True
        else:
            print("✗ Insufficient target layers found")
            return False
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cam_initialization():
    """Test CAM initialization with fixed ResNet"""
    print("\n" + "="*50)
    print("Testing CAM initialization...")
    
    try:
        from slr_network import SLRModel
        from generate_cam_dta import DynamicTemporalAttentionCAM
        
        # Create model
        model = SLRModel(
            num_classes=1000,
            c2d_type='resnet18',
            conv_type=2,
            use_bn=1
        )
        
        # Initialize CAM
        target_layers = ['conv2d.corr2.fusion.0', 'conv2d.corr1.fusion.0']
        dta_cam = DynamicTemporalAttentionCAM(model, target_layers=target_layers)
        
        print("✓ DynamicTemporalAttentionCAM initialized successfully")
        print(f"✓ Target layers: {dta_cam.target_layers}")
        print(f"✓ Hooks registered: {len(dta_cam.hooks)}")
        
        # Clean up
        dta_cam.remove_hooks()
        print("✓ Hooks removed successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_forward_pass():
    """Test forward pass with fixed model"""
    print("\n" + "="*50)
    print("Testing forward pass...")
    
    try:
        import torch
        from slr_network import SLRModel
        
        # Create model
        model = SLRModel(
            num_classes=1000,
            c2d_type='resnet18',
            conv_type=2,
            use_bn=1
        )
        model.eval()
        
        # Create dummy input
        batch_size = 1
        channels = 3
        temporal = 16
        height = 224
        width = 224
        
        dummy_input = torch.randn(batch_size, channels, temporal, height, width)
        vid_lgt = torch.LongTensor([temporal])
        
        print(f"✓ Created dummy input: {dummy_input.shape}")
        
        # Forward pass
        with torch.no_grad():
            output = model(dummy_input, vid_lgt)
        
        print("✓ Forward pass successful")
        print(f"✓ Output keys: {list(output.keys())}")
        
        if 'sequence_logits' in output:
            seq_logits = output['sequence_logits']
            print(f"✓ Sequence logits shape: {seq_logits.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    print("=== Testing Fixed ResNet Implementation ===\n")
    
    tests = [
        ("ResNet Creation", test_resnet_creation),
        ("CAM Initialization", test_cam_initialization),
        ("Forward Pass", test_forward_pass)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"Running: {test_name}")
        print("-" * 50)
        
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status:<8} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Fixed ResNet implementation is working.")
        print("\nYou can now try:")
        print("  python generate_cam_dta.py --help")
        print("  python quick_start_dta.py --test_only")
    else:
        print(f"\n⚠ {total-passed} tests failed. Check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
