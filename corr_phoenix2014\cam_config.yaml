# Grad-CAM Configuration File for Sign Language Recognition

# Model Configuration
model:
  checkpoint: "./work_dir/baseline_res18_attention/_best_model.pt"
  num_classes: 1000
  c2d_type: "resnet18"
  conv_type: 2
  use_bn: 1

# Dataset Configuration
dataset:
  name: "phoenix2014"
  prefix: "./dataset/phoenix2014/phoenix-2014-multisigner"
  dict_path: "./preprocess/phoenix2014/gloss_dict.npy"
  dev_info_path: "./preprocess/phoenix2014/dev_info.npy"

# CAM Generation Settings
cam:
  target_layers:
    - "conv2d.corr2.conv_back"  # Main correlation attention layer
    - "conv2d.corr1.conv_back"  # Earlier correlation layer
    - "conv2d.layer4.1.conv2"   # ResNet layer
    - "conv2d.layer3.1.conv2"   # ResNet layer
  
  # Visualization settings
  alpha: 0.4  # Alpha blending factor for overlay
  save_original: true
  colormap: "COLORMAP_JET"  # OpenCV colormap
  
  # Output settings
  output_dir: "./CAM_results"
  create_video_summary: true
  create_layer_comparison: true

# Processing Settings
processing:
  gpu_id: 0
  batch_size: 1  # Currently only supports batch_size=1
  
  # Video preprocessing
  center_crop: 224
  resize_factor: 1.0
  normalization:
    mean: 0.0
    std: 127.5

# Batch Processing Settings
batch:
  start_id: 0
  end_id: null  # null means process all videos
  max_videos: 100  # Maximum number of videos to process
  
  # Error handling
  continue_on_error: true
  save_failed_list: true

# Visualization Settings
visualization:
  comparison_types:
    - "layers"  # Compare different layers for same video
    - "videos"  # Compare same layer across different videos
  
  max_frames_per_comparison: 8
  figure_size: [12, 8]
  dpi: 150
  
  # Frame selection strategy
  frame_selection: "uniform"  # "uniform", "random", "manual"
  manual_frame_indices: [0, 5, 10, 15, 20]  # Used when frame_selection is "manual"

# Experimental Settings
experiments:
  # Different layer combinations to test
  layer_combinations:
    correlation_only:
      - "conv2d.corr2.conv_back"
      - "conv2d.corr1.conv_back"
    
    resnet_only:
      - "conv2d.layer4.1.conv2"
      - "conv2d.layer3.1.conv2"
      - "conv2d.layer2.1.conv2"
    
    mixed:
      - "conv2d.corr2.conv_back"
      - "conv2d.layer4.1.conv2"
      - "conv2d.layer3.1.conv2"
  
  # Different alpha values to test
  alpha_values: [0.2, 0.4, 0.6, 0.8]
  
  # Class-specific CAM generation
  target_classes:
    - null  # Use predicted class
    - 0     # Specific class index
    - 1     # Another specific class

# Logging and Debug
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  save_logs: true
  log_file: "./cam_generation.log"
  
debug:
  save_intermediate_features: false
  save_gradients: false
  verbose_hooks: false
