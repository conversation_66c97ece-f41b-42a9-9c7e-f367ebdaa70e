# Grad-CAM Configuration File for Sign Language Recognition

# Model Configuration
model:
  checkpoint: "./work_dir/baseline_res18_attention/_best_model.pt"
  num_classes: 1000
  c2d_type: "resnet18"
  conv_type: 2
  use_bn: 1

# DynamicTemporalAttention Parameters
attention_params:
  max_window_size: 11
  kernel_sizes: [5, 9, 13]
  reduction_ratio: 16

# Dataset Configuration
dataset:
  name: "phoenix2014"
  prefix: "./dataset/phoenix2014/phoenix-2014-multisigner"
  dict_path: "./preprocess/phoenix2014/gloss_dict.npy"
  dev_info_path: "./preprocess/phoenix2014/dev_info.npy"

# CAM Generation Settings for DynamicTemporalAttention
cam:
  target_layers:
    - "conv2d.corr2.fusion.0"      # Main DTA fusion layer
    - "conv2d.corr2.temporal_gate.0" # Temporal gate layer
    - "conv2d.corr1.fusion.0"      # Earlier DTA fusion layer
    - "conv2d.corr1.temporal_gate.0" # Earlier temporal gate
    - "conv2d.layer4.1.conv2"      # High-level ResNet layer
    - "conv2d.layer3.1.conv2"      # Mid-level ResNet layer
  
  # Visualization settings
  alpha: 0.4  # Alpha blending factor for overlay
  save_original: true
  colormap: "COLORMAP_JET"  # OpenCV colormap

  # DynamicTemporalAttention specific settings
  visualize_attention_weights: true  # Generate attention weight plots
  visualize_window_weights: true     # Visualize dynamic window weights
  visualize_temporal_gates: true     # Visualize temporal gate activations

  # Output settings
  output_dir: "./DTA_CAM_results"
  create_video_summary: true
  create_layer_comparison: true
  create_attention_summary: true     # Create attention weight summaries

# Processing Settings
processing:
  gpu_id: 0
  batch_size: 1  # Currently only supports batch_size=1
  
  # Video preprocessing
  center_crop: 224
  resize_factor: 1.0
  normalization:
    mean: 0.0
    std: 127.5

# Batch Processing Settings
batch:
  start_id: 0
  end_id: null  # null means process all videos
  max_videos: 100  # Maximum number of videos to process
  
  # Error handling
  continue_on_error: true
  save_failed_list: true

# Visualization Settings
visualization:
  comparison_types:
    - "layers"  # Compare different layers for same video
    - "videos"  # Compare same layer across different videos
  
  max_frames_per_comparison: 8
  figure_size: [12, 8]
  dpi: 150
  
  # Frame selection strategy
  frame_selection: "uniform"  # "uniform", "random", "manual"
  manual_frame_indices: [0, 5, 10, 15, 20]  # Used when frame_selection is "manual"

# Experimental Settings for DynamicTemporalAttention
experiments:
  # Different layer combinations to test
  layer_combinations:
    dta_fusion_only:
      - "conv2d.corr2.fusion.0"
      - "conv2d.corr1.fusion.0"

    dta_gates_only:
      - "conv2d.corr2.temporal_gate.0"
      - "conv2d.corr1.temporal_gate.0"

    dta_complete:
      - "conv2d.corr2.fusion.0"
      - "conv2d.corr2.temporal_gate.0"
      - "conv2d.corr1.fusion.0"
      - "conv2d.corr1.temporal_gate.0"

    resnet_only:
      - "conv2d.layer4.1.conv2"
      - "conv2d.layer3.1.conv2"
      - "conv2d.layer2.1.conv2"

    mixed_dta_resnet:
      - "conv2d.corr2.fusion.0"
      - "conv2d.layer4.1.conv2"
      - "conv2d.layer3.1.conv2"
  
  # Different alpha values to test
  alpha_values: [0.2, 0.4, 0.6, 0.8]
  
  # Class-specific CAM generation
  target_classes:
    - null  # Use predicted class
    - 0     # Specific class index
    - 1     # Another specific class

# Logging and Debug
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  save_logs: true
  log_file: "./cam_generation.log"
  
debug:
  save_intermediate_features: false
  save_gradients: false
  verbose_hooks: false
