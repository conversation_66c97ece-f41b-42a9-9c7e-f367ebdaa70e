#!/usr/bin/env python3
"""
Test script to verify CAM setup and basic functionality
"""

import os
import sys
import torch
import numpy as np
import cv2
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"✗ PyTorch import failed: {e}")
        return False
    
    try:
        import cv2
        print(f"✓ OpenCV {cv2.__version__}")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✓ Matplotlib")
    except ImportError as e:
        print(f"✗ Matplotlib import failed: {e}")
        return False
    
    try:
        import yaml
        print("✓ PyYAML")
    except ImportError as e:
        print(f"✗ PyYAML import failed: {e}")
        return False
    
    try:
        from slr_network import SLRModel
        print("✓ SLRModel")
    except ImportError as e:
        print(f"✗ SLRModel import failed: {e}")
        return False
    
    try:
        from utils.device import GpuDataParallel
        print("✓ GpuDataParallel")
    except ImportError as e:
        print(f"✗ GpuDataParallel import failed: {e}")
        return False
    
    try:
        from generate_cam import GradCAM
        print("✓ GradCAM")
    except ImportError as e:
        print(f"✗ GradCAM import failed: {e}")
        return False
    
    return True


def test_cuda():
    """Test CUDA availability"""
    print("\nTesting CUDA...")
    
    if torch.cuda.is_available():
        print(f"✓ CUDA available: {torch.cuda.get_device_name(0)}")
        print(f"✓ CUDA version: {torch.version.cuda}")
        return True
    else:
        print("⚠ CUDA not available, will use CPU")
        return False


def test_file_structure():
    """Test if required files and directories exist"""
    print("\nTesting file structure...")
    
    required_files = [
        'slr_network.py',
        'generate_cam.py',
        'cam_config.yaml',
        'utils/device.py',
        'utils/video_augmentation.py'
    ]
    
    required_dirs = [
        'utils',
        'modules',
        'preprocess'
    ]
    
    all_exist = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} not found")
            all_exist = False
    
    for dir_path in required_dirs:
        if os.path.isdir(dir_path):
            print(f"✓ {dir_path}/")
        else:
            print(f"✗ {dir_path}/ not found")
            all_exist = False
    
    return all_exist


def test_model_loading():
    """Test if model can be loaded"""
    print("\nTesting model loading...")
    
    try:
        from slr_network import SLRModel
        model = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)
        print("✓ Model created successfully")
        
        # Test model structure
        total_params = sum(p.numel() for p in model.parameters())
        print(f"✓ Total parameters: {total_params:,}")
        
        # Test target layers exist
        target_layers = ['conv2d.corr2.conv_back', 'conv2d.corr1.conv_back']
        for layer_name in target_layers:
            layer = model
            try:
                for attr in layer_name.split('.'):
                    layer = getattr(layer, attr)
                print(f"✓ Layer {layer_name} found")
            except AttributeError:
                print(f"✗ Layer {layer_name} not found")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        return False


def test_gradcam_initialization():
    """Test GradCAM class initialization"""
    print("\nTesting GradCAM initialization...")
    
    try:
        from slr_network import SLRModel
        from generate_cam import GradCAM
        
        model = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)
        grad_cam = GradCAM(model, target_layers=['conv2d.corr2.conv_back'])
        
        print("✓ GradCAM initialized successfully")
        print(f"✓ Target layers: {grad_cam.target_layers}")
        print(f"✓ Hooks registered: {len(grad_cam.hooks)}")
        
        # Clean up
        grad_cam.remove_hooks()
        print("✓ Hooks removed successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ GradCAM initialization failed: {e}")
        return False


def test_data_paths():
    """Test if data paths exist"""
    print("\nTesting data paths...")
    
    # Check common data paths
    data_paths = [
        './dataset/phoenix2014',
        './preprocess/phoenix2014',
        './work_dir'
    ]
    
    found_paths = []
    for path in data_paths:
        if os.path.exists(path):
            print(f"✓ {path}")
            found_paths.append(path)
        else:
            print(f"⚠ {path} not found (optional)")
    
    if found_paths:
        print(f"✓ Found {len(found_paths)} data directories")
        return True
    else:
        print("⚠ No data directories found - you'll need to set up data paths")
        return False


def create_sample_config():
    """Create a sample configuration for testing"""
    print("\nCreating sample test configuration...")
    
    sample_config = """
# Test configuration for CAM generation
model:
  checkpoint: "./work_dir/baseline_res18_attention/_best_model.pt"
  num_classes: 1000
  c2d_type: "resnet18"
  conv_type: 2
  use_bn: 1

dataset:
  name: "phoenix2014"
  prefix: "./dataset/phoenix2014/phoenix-2014-multisigner"
  dict_path: "./preprocess/phoenix2014/gloss_dict.npy"

cam:
  target_layers:
    - "conv2d.corr2.conv_back"
  alpha: 0.4
  save_original: true
  output_dir: "./test_CAM_results"

processing:
  gpu_id: 0
"""
    
    try:
        with open('test_cam_config.yaml', 'w') as f:
            f.write(sample_config)
        print("✓ Sample configuration created: test_cam_config.yaml")
        return True
    except Exception as e:
        print(f"✗ Failed to create sample configuration: {e}")
        return False


def main():
    """Run all tests"""
    print("=== CAM Setup Test ===\n")
    
    tests = [
        ("Import Test", test_imports),
        ("CUDA Test", test_cuda),
        ("File Structure Test", test_file_structure),
        ("Model Loading Test", test_model_loading),
        ("GradCAM Initialization Test", test_gradcam_initialization),
        ("Data Paths Test", test_data_paths),
        ("Sample Config Creation", create_sample_config)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status:<8} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your CAM setup is ready.")
        print("\nNext steps:")
        print("1. Set up your data paths in cam_config.yaml")
        print("2. Place your model checkpoint in the specified location")
        print("3. Run: python generate_cam.py --help")
    else:
        print(f"\n⚠ {total-passed} tests failed. Please fix the issues above.")
        
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
