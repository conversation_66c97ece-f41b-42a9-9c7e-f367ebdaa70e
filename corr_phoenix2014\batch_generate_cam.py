#!/usr/bin/env python3
"""
Batch Grad-CAM generation script for multiple videos
"""

import os
import argparse
import subprocess
import numpy as np
from tqdm import tqdm


def main():
    parser = argparse.ArgumentParser(description='Batch generate Grad-CAM for multiple videos')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to model checkpoint')
    parser.add_argument('--dataset', type=str, default='phoenix2014',
                        help='Dataset name')
    parser.add_argument('--prefix', type=str, required=True,
                        help='Dataset prefix path')
    parser.add_argument('--dict_path', type=str, required=True,
                        help='Path to gloss dictionary')
    parser.add_argument('--output_dir', type=str, default='./batch_CAM_results',
                        help='Output directory for all CAM results')
    parser.add_argument('--start_id', type=int, default=0,
                        help='Starting video ID')
    parser.add_argument('--end_id', type=int, default=None,
                        help='Ending video ID (if None, process all)')
    parser.add_argument('--gpu_id', type=int, default=0,
                        help='GPU ID to use')
    parser.add_argument('--target_layers', type=str, nargs='+',
                        default=['conv2d.corr2.conv_back'],
                        help='Target layers for CAM generation')
    parser.add_argument('--alpha', type=float, default=0.4,
                        help='Alpha blending factor for CAM overlay')
    parser.add_argument('--save_original', action='store_true',
                        help='Save original frames alongside CAM')
    
    args = parser.parse_args()
    
    # Load video list to determine range
    inputs_list = np.load(f"./preprocess/{args.dataset}/dev_info.npy", allow_pickle=True).item()
    total_videos = len(inputs_list)
    
    if args.end_id is None:
        args.end_id = total_videos - 1
    
    args.end_id = min(args.end_id, total_videos - 1)
    
    print(f"Processing videos {args.start_id} to {args.end_id} (total: {args.end_id - args.start_id + 1})")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Process each video
    failed_videos = []
    
    for video_id in tqdm(range(args.start_id, args.end_id + 1), desc="Processing videos"):
        try:
            # Prepare command
            cmd = [
                'python', 'generate_cam.py',
                '--checkpoint', args.checkpoint,
                '--dataset', args.dataset,
                '--prefix', args.prefix,
                '--dict_path', args.dict_path,
                '--select_id', str(video_id),
                '--gpu_id', str(args.gpu_id),
                '--output_dir', os.path.join(args.output_dir, f'video_{video_id:04d}'),
                '--target_layers'] + args.target_layers + [
                '--alpha', str(args.alpha)
            ]
            
            if args.save_original:
                cmd.append('--save_original')
            
            # Run command
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
            
            if result.returncode != 0:
                print(f"Failed to process video {video_id}")
                print(f"Error: {result.stderr}")
                failed_videos.append(video_id)
            else:
                print(f"Successfully processed video {video_id}")
                
        except Exception as e:
            print(f"Exception while processing video {video_id}: {e}")
            failed_videos.append(video_id)
    
    # Summary
    print(f"\nProcessing completed!")
    print(f"Successfully processed: {args.end_id - args.start_id + 1 - len(failed_videos)} videos")
    print(f"Failed: {len(failed_videos)} videos")
    
    if failed_videos:
        print(f"Failed video IDs: {failed_videos}")
        
        # Save failed video list
        failed_file = os.path.join(args.output_dir, 'failed_videos.txt')
        with open(failed_file, 'w') as f:
            for vid_id in failed_videos:
                f.write(f"{vid_id}\n")
        print(f"Failed video IDs saved to: {failed_file}")


if __name__ == "__main__":
    main()
