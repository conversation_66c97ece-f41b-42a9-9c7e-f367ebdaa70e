#!/usr/bin/env python3
"""
Quick start script for CAM generation with minimal setup
"""

import os
import argparse
import sys
from pathlib import Path

def find_checkpoint():
    """Find available model checkpoints"""
    checkpoint_paths = [
        './work_dir/baseline_res18_attention/_best_model.pt',
        './work_dir/baseline_res18_attention/best_model.pt',
        './pretrain_model/dev_18.90_PHOENIX14-T.pt'
    ]
    
    # Search in work_dir subdirectories
    work_dir = Path('./work_dir')
    if work_dir.exists():
        for subdir in work_dir.iterdir():
            if subdir.is_dir():
                for checkpoint_file in ['_best_model.pt', 'best_model.pt', 'model.pt']:
                    checkpoint_path = subdir / checkpoint_file
                    if checkpoint_path.exists():
                        checkpoint_paths.append(str(checkpoint_path))
    
    # Return first existing checkpoint
    for path in checkpoint_paths:
        if os.path.exists(path):
            return path
    
    return None


def find_dataset():
    """Find available dataset"""
    dataset_paths = [
        './dataset/phoenix2014/phoenix-2014-multisigner',
        './dataset/phoenix2014',
        '../dataset/phoenix2014/phoenix-2014-multisigner'
    ]
    
    for path in dataset_paths:
        if os.path.exists(path):
            return path
    
    return None


def find_dict_path():
    """Find gloss dictionary"""
    dict_paths = [
        './preprocess/phoenix2014/gloss_dict.npy',
        './preprocess/gloss_dict.npy',
        '../preprocess/phoenix2014/gloss_dict.npy'
    ]
    
    for path in dict_paths:
        if os.path.exists(path):
            return path
    
    return None


def check_requirements():
    """Check if basic requirements are met"""
    print("Checking requirements...")
    
    # Check imports
    try:
        import torch
        import cv2
        import numpy as np
        from slr_network import SLRModel
        from utils.device import GpuDataParallel
        print("✓ All required modules available")
    except ImportError as e:
        print(f"✗ Missing required module: {e}")
        return False
    
    # Check CUDA
    if torch.cuda.is_available():
        print(f"✓ CUDA available: {torch.cuda.get_device_name(0)}")
    else:
        print("⚠ CUDA not available, using CPU")
    
    return True


def setup_paths():
    """Automatically find and setup paths"""
    print("Setting up paths...")
    
    checkpoint = find_checkpoint()
    if checkpoint:
        print(f"✓ Found checkpoint: {checkpoint}")
    else:
        print("✗ No checkpoint found")
        print("Please place a model checkpoint in one of these locations:")
        print("  - ./work_dir/baseline_res18_attention/_best_model.pt")
        print("  - ./pretrain_model/dev_18.90_PHOENIX14-T.pt")
        return None, None, None
    
    dataset_prefix = find_dataset()
    if dataset_prefix:
        print(f"✓ Found dataset: {dataset_prefix}")
    else:
        print("✗ No dataset found")
        print("Please set up dataset in: ./dataset/phoenix2014/phoenix-2014-multisigner")
        return checkpoint, None, None
    
    dict_path = find_dict_path()
    if dict_path:
        print(f"✓ Found dictionary: {dict_path}")
    else:
        print("✗ No gloss dictionary found")
        print("Please set up dictionary in: ./preprocess/phoenix2014/gloss_dict.npy")
        return checkpoint, dataset_prefix, None
    
    return checkpoint, dataset_prefix, dict_path


def run_basic_cam(checkpoint, dataset_prefix, dict_path, video_id=0, gpu_id=0):
    """Run basic CAM generation"""
    print(f"\nGenerating CAM for video {video_id}...")
    
    cmd = [
        sys.executable, 'generate_cam.py',
        '--checkpoint', checkpoint,
        '--dataset', 'phoenix2014',
        '--prefix', dataset_prefix,
        '--dict_path', dict_path,
        '--select_id', str(video_id),
        '--gpu_id', str(gpu_id),
        '--output_dir', './quick_CAM_results',
        '--target_layers', 'conv2d.corr2.conv_back',
        '--alpha', '0.4',
        '--save_original'
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    
    import subprocess
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ CAM generation completed successfully!")
        print(f"Output saved to: ./quick_CAM_results")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ CAM generation failed:")
        print(f"Error: {e.stderr}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Quick start CAM generation')
    parser.add_argument('--video_id', type=int, default=0,
                        help='Video ID to process (default: 0)')
    parser.add_argument('--gpu_id', type=int, default=0,
                        help='GPU ID to use (default: 0)')
    parser.add_argument('--test_only', action='store_true',
                        help='Only test setup, do not generate CAM')
    
    args = parser.parse_args()
    
    print("=== Quick Start CAM Generation ===\n")
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please install missing dependencies.")
        return False
    
    # Setup paths
    checkpoint, dataset_prefix, dict_path = setup_paths()
    
    if not all([checkpoint, dataset_prefix, dict_path]):
        print("\n❌ Path setup failed. Please check the missing files above.")
        return False
    
    if args.test_only:
        print("\n✅ Test completed successfully! All paths are set up correctly.")
        print("\nTo generate CAM, run:")
        print(f"python quick_start_cam.py --video_id {args.video_id}")
        return True
    
    # Run CAM generation
    success = run_basic_cam(checkpoint, dataset_prefix, dict_path, 
                           args.video_id, args.gpu_id)
    
    if success:
        print("\n🎉 Quick start completed successfully!")
        print("\nNext steps:")
        print("1. Check the results in ./quick_CAM_results")
        print("2. Try different videos: python quick_start_cam.py --video_id 1")
        print("3. Use advanced features: python generate_cam.py --help")
        print("4. Batch processing: python batch_generate_cam.py --help")
    else:
        print("\n❌ Quick start failed. Please check the error messages above.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
