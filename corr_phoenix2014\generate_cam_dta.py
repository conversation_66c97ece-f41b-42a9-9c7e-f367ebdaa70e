#!/usr/bin/env python3
"""
Grad-CAM generation specifically for DynamicTemporalAttention architecture
"""

import os
import cv2
import glob
import torch
import torch.nn.functional as F
import numpy as np
import argparse
import matplotlib.pyplot as plt
from collections import OrderedDict
import warnings
warnings.filterwarnings('ignore')

# 修改导入路径
from slr_network import SLRModel
from utils.device import GpuDataParallel


class DynamicTemporalAttentionCAM:
    """
    Specialized Grad-CAM for DynamicTemporalAttention architecture
    """
    def __init__(self, model, target_layers=None):
        self.model = model
        # 针对新架构的默认目标层
        self.target_layers = target_layers or [
            'conv2d.corr2.fusion.0',      # Main attention fusion layer
            'conv2d.corr2.temporal_gate.0', # Temporal gate layer
            'conv2d.corr1.fusion.0',      # Earlier attention fusion
            'conv2d.layer4.1.conv2',      # High-level ResNet features
            'conv2d.layer3.1.conv2',      # Mid-level ResNet features
        ]
        self.gradients = {}
        self.activations = {}
        self.attention_weights = {}  # Store attention weights
        self.hooks = []
        
        # Register hooks
        self._register_hooks()
    
    def _register_hooks(self):
        """Register forward and backward hooks for target layers"""
        for layer_name in self.target_layers:
            layer = self._get_layer_by_name(layer_name)
            if layer is not None:
                # Forward hook to capture activations
                def make_forward_hook(name):
                    def hook(module, input, output):
                        self._save_activation(name, output)
                        # Special handling for attention modules
                        if 'corr' in name and hasattr(module, 'weight'):
                            self._save_attention_weights(name, module)
                    return hook
                
                # Backward hook to capture gradients  
                def make_backward_hook(name):
                    def hook(module, grad_input, grad_output):
                        if grad_output[0] is not None:
                            self._save_gradient(name, grad_output[0])
                    return hook
                
                forward_hook = layer.register_forward_hook(make_forward_hook(layer_name))
                backward_hook = layer.register_backward_hook(make_backward_hook(layer_name))
                self.hooks.extend([forward_hook, backward_hook])
        
        # Also register hooks for attention modules to capture their internal states
        self._register_attention_hooks()
    
    def _register_attention_hooks(self):
        """Register hooks specifically for DynamicTemporalAttention modules"""
        attention_modules = ['corr1', 'corr2', 'corr3']
        
        for module_name in attention_modules:
            module = getattr(self.model.conv2d, module_name, None)
            if module is not None:
                # Hook for window predictor if it exists
                if hasattr(module, 'window_predictor'):
                    def make_window_hook(name):
                        def hook(module, input, output):
                            self.attention_weights[f'{name}_window_weights'] = output.detach()
                        return hook
                    
                    window_hook = module.window_predictor.register_forward_hook(
                        make_window_hook(f'conv2d.{module_name}')
                    )
                    self.hooks.append(window_hook)
                
                # Hook for temporal gate
                if hasattr(module, 'temporal_gate'):
                    def make_gate_hook(name):
                        def hook(module, input, output):
                            self.attention_weights[f'{name}_gate_weights'] = output.detach()
                        return hook
                    
                    gate_hook = module.temporal_gate.register_forward_hook(
                        make_gate_hook(f'conv2d.{module_name}')
                    )
                    self.hooks.append(gate_hook)
    
    def _get_layer_by_name(self, layer_name):
        """Get layer by its name path"""
        layer = self.model
        for attr in layer_name.split('.'):
            if hasattr(layer, attr):
                layer = getattr(layer, attr)
            else:
                print(f"Warning: Layer {layer_name} not found")
                return None
        return layer
    
    def _save_activation(self, name, activation):
        """Save activation for later use"""
        self.activations[name] = activation.detach()
    
    def _save_gradient(self, name, gradient):
        """Save gradient for later use"""
        self.gradients[name] = gradient.detach()
    
    def _save_attention_weights(self, name, module):
        """Save attention weights if available"""
        if hasattr(module, 'weight') and module.weight is not None:
            self.attention_weights[name] = module.weight.detach()
    
    def generate_cam(self, input_tensor, vid_lgt=None, class_idx=None, layer_name=None):
        """
        Generate Grad-CAM for given input with DynamicTemporalAttention

        Args:
            input_tensor: Input video tensor (B, T, C, H, W) - SLRModel format
            vid_lgt: Video length tensor
            class_idx: Target class index for CAM generation
            layer_name: Specific layer to generate CAM for

        Returns:
            cam: Generated CAM (T, H, W)
            attention_info: Attention weights and other info
        """
        # Clear previous data
        self.gradients.clear()
        self.activations.clear()
        self.attention_weights.clear()

        # Forward pass - use training mode for RNN backward compatibility
        self.model.train()
        input_tensor.requires_grad_(True)

        # Prepare video length if not provided
        if vid_lgt is None:
            # For SLRModel, temporal dimension is at index 1
            vid_lgt = torch.LongTensor([input_tensor.shape[1]]).to(input_tensor.device)

        # Get model output
        output = self.model(input_tensor, vid_lgt)
        
        # Use the sequence logits for CAM generation
        logits = output['sequence_logits']  # Shape: (T, B, num_classes)
        
        # If no class specified, use the predicted class
        if class_idx is None:
            mean_logits = logits.mean(0)  # Average over time: (B, num_classes)
            class_idx = torch.argmax(mean_logits, dim=1).item()
        
        # Compute loss for the target class
        target_score = logits[:, 0, class_idx].sum()  # Sum over time dimension
        
        # Backward pass
        self.model.zero_grad()
        target_score.backward(retain_graph=True)
        
        # Generate CAM for specified layer
        target_layer = layer_name or self.target_layers[0]
        
        if target_layer not in self.gradients or target_layer not in self.activations:
            print(f"Warning: No gradients or activations found for layer {target_layer}")
            return None, None
        
        gradients = self.gradients[target_layer]
        activations = self.activations[target_layer]
        
        # Generate CAM based on activation shape
        cam = self._compute_cam(activations, gradients)
        
        # Get attention weights for visualization
        attention_info = self._extract_attention_info(target_layer)
        
        return cam, attention_info
    
    def _compute_cam(self, activations, gradients):
        """Compute CAM from activations and gradients"""
        if len(activations.shape) == 5:  # (B, C, T, H, W)
            # Compute weights by global average pooling of gradients
            weights = torch.mean(gradients, dim=(3, 4), keepdim=True)  # (B, C, T, 1, 1)
            
            # Generate CAM
            cam = torch.sum(weights * activations, dim=1)  # (B, T, H, W)
            cam = F.relu(cam)  # Apply ReLU
            
            # Normalize CAM
            cam = cam.squeeze(0)  # Remove batch dimension (T, H, W)
            
        elif len(activations.shape) == 4:  # (B, C, H, W)
            # Compute weights by global average pooling of gradients
            weights = torch.mean(gradients, dim=(2, 3), keepdim=True)  # (B, C, 1, 1)
            
            # Generate CAM
            cam = torch.sum(weights * activations, dim=1)  # (B, H, W)
            cam = F.relu(cam)  # Apply ReLU
            
            # Normalize CAM
            cam = cam.squeeze(0)  # Remove batch dimension (H, W)
            cam = cam.unsqueeze(0)  # Add temporal dimension (1, H, W)
            
        else:
            print(f"Unsupported activation shape: {activations.shape}")
            return None
        
        return cam
    
    def _extract_attention_info(self, target_layer):
        """Extract attention-related information"""
        attention_info = {}
        
        # Extract window weights if available
        for key, value in self.attention_weights.items():
            if 'window_weights' in key:
                attention_info['window_weights'] = value.cpu().numpy()
            elif 'gate_weights' in key:
                attention_info['gate_weights'] = value.cpu().numpy()
        
        return attention_info
    
    def visualize_attention_weights(self, attention_info, output_dir, video_name):
        """Visualize attention weights separately"""
        if not attention_info:
            return
        
        attention_dir = os.path.join(output_dir, 'attention_weights')
        os.makedirs(attention_dir, exist_ok=True)
        
        # Visualize window weights
        if 'window_weights' in attention_info:
            window_weights = attention_info['window_weights']  # (B, num_windows, T)
            
            plt.figure(figsize=(12, 6))
            plt.imshow(window_weights[0], aspect='auto', cmap='viridis')
            plt.colorbar()
            plt.title(f'Window Weights - {video_name}')
            plt.xlabel('Time Steps')
            plt.ylabel('Window Size')
            plt.savefig(os.path.join(attention_dir, f'{video_name}_window_weights.png'), 
                       dpi=150, bbox_inches='tight')
            plt.close()
        
        # Visualize gate weights
        if 'gate_weights' in attention_info:
            gate_weights = attention_info['gate_weights']  # (B, C, T, H, W)
            
            # Average over channels and spatial dimensions
            gate_avg = gate_weights[0].mean(dim=(0, 2, 3)).cpu().numpy()  # (T,)
            
            plt.figure(figsize=(10, 4))
            plt.plot(gate_avg)
            plt.title(f'Temporal Gate Weights - {video_name}')
            plt.xlabel('Time Steps')
            plt.ylabel('Gate Value')
            plt.grid(True)
            plt.savefig(os.path.join(attention_dir, f'{video_name}_gate_weights.png'), 
                       dpi=150, bbox_inches='tight')
            plt.close()
    
    def remove_hooks(self):
        """Remove all registered hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()


def load_model_and_data_dta(args):
    """Load model and data for DynamicTemporalAttention architecture"""
    # 设置设备
    device = GpuDataParallel()
    device.set_device(args.gpu_id)

    # 加载模型 - 注意这里需要传入attention_params
    print(f"Loading model from {args.checkpoint}")

    # 加载数据和字典
    print(f"Loading data for video ID {args.select_id}")
    gloss_dict = np.load(args.dict_path, allow_pickle=True).item()
    inputs_list = np.load(f"./preprocess/{args.dataset}/dev_info.npy", allow_pickle=True).item()

    if args.select_id >= len(inputs_list):
        raise ValueError(f"Video ID {args.select_id} out of range. Max ID: {len(inputs_list)-1}")

    # 从检查点推断正确的模型配置
    print("Analyzing checkpoint to determine model configuration...")
    checkpoint = torch.load(args.checkpoint, map_location='cpu')
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint

    # 从权重推断配置
    num_classes = 1000  # 默认值
    attention_params = None

    # 检查分类器权重来确定类别数
    for key in state_dict.keys():
        if 'classifier.weight' in key:
            num_classes = state_dict[key].shape[1]
            print(f"Detected num_classes from checkpoint: {num_classes}")
            break

    # 检查注意力模块权重来确定注意力参数
    for key in state_dict.keys():
        if 'corr1.window_predictor.2.weight' in key:
            max_window_size = state_dict[key].shape[0]
            print(f"Detected max_window_size from checkpoint: {max_window_size}")
            break

    # 检查卷积核尺寸
    kernel_sizes = []
    for i in range(3):  # 通常有3个不同尺寸的卷积核
        key = f'conv2d.corr1.window_convs.{i}.weight'
        if key in state_dict:
            kernel_size = state_dict[key].shape[2]
            kernel_sizes.append(kernel_size)

    if kernel_sizes:
        print(f"Detected kernel_sizes from checkpoint: {kernel_sizes}")
        attention_params = {
            'max_window_size': max_window_size,
            'kernel_sizes': kernel_sizes,
            'reduction_ratio': 16  # 使用默认值
        }

    # 如果有attention参数配置文件，优先使用它
    if hasattr(args, 'attention_config') and args.attention_config and os.path.exists(args.attention_config):
        import yaml
        try:
            with open(args.attention_config, 'r') as f:
                config = yaml.safe_load(f)
                config_attention_params = config.get('attention_params', None)
                if config_attention_params:
                    attention_params = config_attention_params
                    print(f"Using attention params from config file: {attention_params}")
        except Exception as e:
            print(f"Failed to load attention config: {e}")

    # 创建模型
    print(f"Creating SLRModel with num_classes={num_classes}, attention_params={attention_params is not None}")
    try:
        model = SLRModel(
            num_classes=num_classes,
            c2d_type='resnet18',
            conv_type=2,
            use_bn=1,
            gloss_dict=gloss_dict,
            attention_params=attention_params
        )
        print("✓ Model created successfully with inferred configuration")
    except Exception as e:
        print(f"Failed to create model with inferred config: {e}")
        print("Trying with default configuration...")
        try:
            model = SLRModel(
                num_classes=num_classes,
                c2d_type='resnet18',
                conv_type=2,
                use_bn=1,
                gloss_dict=gloss_dict
            )
            print("✓ Model created successfully with default configuration")
        except Exception as e2:
            print(f"Failed to create model: {e2}")
            raise e2
    
    # 加载模型权重
    print("Loading model weights...")
    # Clean state dict keys
    state_dict = OrderedDict([(k.replace('.module', ''), v) for k, v in state_dict.items()])

    try:
        model.load_state_dict(state_dict, strict=True)
        print("✓ Model weights loaded successfully (strict=True)")
    except Exception as e:
        print(f"Failed to load with strict=True: {e}")
        print("Trying with strict=False...")
        try:
            model.load_state_dict(state_dict, strict=False)
            print("✓ Model weights loaded successfully (strict=False)")
        except Exception as e2:
            print(f"Failed to load weights: {e2}")
            print("Continuing without pretrained weights...")

    model = model.to(device.output_device)
    model.eval()

    video_info = inputs_list[args.select_id]
    name = video_info['fileid']
    print(f'Processing video: {name}')

    # Load images
    img_folder = os.path.join(args.prefix, "features/fullFrame-256x256px/" + video_info['folder'])
    img_list = sorted(glob.glob(img_folder + "/*.png"))

    if not img_list:
        raise FileNotFoundError(f"No images found in {img_folder}")

    img_list = [cv2.cvtColor(cv2.imread(img_path), cv2.COLOR_BGR2RGB) for img_path in img_list]

    # Process labels
    label_list = []
    for phase in video_info['label'].split(" "):
        if phase == '':
            continue
        if phase in gloss_dict.keys():
            label_list.append(gloss_dict[phase][0])

    return model, device, img_list, label_list, name


def preprocess_video_dta(img_list, label_list, device):
    """Preprocess video data for DynamicTemporalAttention"""
    from utils import video_augmentation
    transform = video_augmentation.Compose([
        video_augmentation.CenterCrop(224),
        video_augmentation.Resize(1.0),
        video_augmentation.ToTensor(),
    ])

    vid, label = transform(img_list, label_list, None)
    vid = vid.float() / 127.5 - 1
    vid = vid.unsqueeze(0)  # Add batch dimension

    print("Video shape:", vid.shape)
    vid = device.data_to_device(vid)
    video_length = torch.LongTensor([vid.shape[2]])  # Note: shape[2] is temporal dimension
    vid_lgt = device.data_to_device(video_length)
    label = device.data_to_device([torch.LongTensor(label)])
    label_lgt = device.data_to_device(torch.LongTensor([len(label_list)]))

    return vid, vid_lgt, label, label_lgt


def visualize_cam_with_attention(cam, attention_info, original_frames, output_dir, video_name,
                                alpha=0.4, save_original=False):
    """
    Visualize CAM with attention information
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Create subdirectory for this video
    video_dir = os.path.join(output_dir, video_name)
    if os.path.exists(video_dir):
        import shutil
        shutil.rmtree(video_dir)
    os.makedirs(video_dir)

    cam_np = cam.cpu().numpy() if torch.is_tensor(cam) else cam
    T, H, W = cam_np.shape

    for t in range(T):
        # Get original frame
        if t < len(original_frames):
            original_frame = original_frames[t]
            if original_frame.shape[:2] != (H, W):
                original_frame = cv2.resize(original_frame, (W, H))
        else:
            original_frame = cv2.resize(original_frames[-1], (W, H))

        # Normalize CAM
        cam_frame = cam_np[t]
        cam_frame = (cam_frame - cam_frame.min()) / (cam_frame.max() - cam_frame.min() + 1e-8)

        # Resize CAM to match frame size
        cam_resized = cv2.resize(cam_frame, (original_frame.shape[1], original_frame.shape[0]))

        # Apply colormap
        heatmap = cv2.applyColorMap((cam_resized * 255).astype(np.uint8), cv2.COLORMAP_JET)

        # Blend with original frame
        blended = cv2.addWeighted(original_frame, 1-alpha, heatmap, alpha, 0)

        # Save images
        frame_name = f"frame_{t:04d}"

        # Save CAM overlay
        cv2.imwrite(os.path.join(video_dir, f"{frame_name}_cam.jpg"),
                   cv2.cvtColor(blended, cv2.COLOR_RGB2BGR))

        # Save heatmap only
        cv2.imwrite(os.path.join(video_dir, f"{frame_name}_heatmap.jpg"), heatmap)

        # Save original frame if requested
        if save_original:
            cv2.imwrite(os.path.join(video_dir, f"{frame_name}_original.jpg"),
                       cv2.cvtColor(original_frame, cv2.COLOR_RGB2BGR))

    print(f'Generated CAM visualizations at {video_dir}')
    return video_dir


def main():
    """Main function for DynamicTemporalAttention CAM generation"""
    parser = argparse.ArgumentParser(description='Generate Grad-CAM for DynamicTemporalAttention architecture')
    parser.add_argument('--checkpoint', type=str, default='./work_dir/baseline_res18_attention/_best_model.pt',
                        help='Path to model checkpoint')
    parser.add_argument('--dataset', type=str, default='phoenix2014',
                        help='Dataset name')
    parser.add_argument('--prefix', type=str, default='./dataset/phoenix2014/phoenix-2014-multisigner',
                        help='Dataset prefix path')
    parser.add_argument('--dict_path', type=str, default='./preprocess/phoenix2014/gloss_dict.npy',
                        help='Path to gloss dictionary')
    parser.add_argument('--select_id', type=int, default=0,
                        help='Video ID to process')
    parser.add_argument('--gpu_id', type=int, default=0,
                        help='GPU ID to use')
    parser.add_argument('--output_dir', type=str, default='./DTA_CAM_results',
                        help='Output directory for CAM images')
    parser.add_argument('--target_layers', type=str, nargs='+',
                        default=['conv2d.corr2.fusion.0', 'conv2d.corr1.fusion.0'],
                        help='Target layers for CAM generation')
    parser.add_argument('--class_idx', type=int, default=None,
                        help='Target class index (if None, use predicted class)')
    parser.add_argument('--save_original', action='store_true',
                        help='Save original frames alongside CAM')
    parser.add_argument('--alpha', type=float, default=0.4,
                        help='Alpha blending factor for CAM overlay')
    parser.add_argument('--attention_config', type=str, default=None,
                        help='Path to attention configuration file')
    parser.add_argument('--visualize_attention', action='store_true',
                        help='Generate attention weight visualizations')

    args = parser.parse_args()

    try:
        # Load model and data
        model, device, img_list, label_list, video_name = load_model_and_data_dta(args)

        # Preprocess video
        vid, vid_lgt, label, label_lgt = preprocess_video_dta(img_list, label_list, device)

        # Initialize DynamicTemporalAttention CAM
        dta_cam = DynamicTemporalAttentionCAM(model, target_layers=args.target_layers)

        print(f"Generating CAM for layers: {args.target_layers}")

        # Generate CAM for each target layer
        for layer_name in args.target_layers:
            print(f"Processing layer: {layer_name}")

            # Generate CAM
            cam, attention_info = dta_cam.generate_cam(
                vid, vid_lgt=vid_lgt, class_idx=args.class_idx, layer_name=layer_name
            )

            if cam is not None:
                # Create layer-specific output directory
                layer_output_dir = os.path.join(args.output_dir, layer_name.replace('.', '_'))

                # Visualize CAM
                visualize_cam_with_attention(
                    cam, attention_info, img_list, layer_output_dir, video_name,
                    alpha=args.alpha, save_original=args.save_original
                )

                # Visualize attention weights if requested
                if args.visualize_attention and attention_info:
                    dta_cam.visualize_attention_weights(attention_info, layer_output_dir, video_name)

            else:
                print(f"Failed to generate CAM for layer {layer_name}")

        # Clean up hooks
        dta_cam.remove_hooks()

        print("DynamicTemporalAttention CAM generation completed successfully!")

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
