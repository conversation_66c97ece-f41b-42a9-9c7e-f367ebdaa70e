# Grad-CAM Visualization for Sign Language Recognition

This directory contains tools for generating and visualizing Grad-CAM (Gradient-weighted Class Activation Mapping) for the correlation-based sign language recognition model.

## Overview

The Grad-CAM implementation provides insights into which spatial and temporal regions the model focuses on when making predictions. This is particularly useful for understanding the attention mechanisms in the correlation-based architecture.

## Files Description

### Core Scripts

1. **`generate_cam.py`** - Main Grad-CAM generation script
   - Implements the GradCAM class with proper gradient computation
   - Supports multiple target layers
   - Generates overlaid visualizations

2. **`batch_generate_cam.py`** - Batch processing script
   - Process multiple videos automatically
   - Error handling and progress tracking
   - Parallel processing support

3. **`advanced_cam_generator.py`** - Configuration-based advanced generator
   - Uses YAML configuration files
   - Supports multiple experiments
   - Automatic comparison generation

4. **`visualize_cam_comparison.py`** - Comparison visualization tool
   - Compare different layers for the same video
   - Compare the same layer across different videos
   - Generate publication-ready figures

### Configuration

5. **`cam_config.yaml`** - Configuration file
   - Model and dataset settings
   - CAM generation parameters
   - Experimental configurations
   - Visualization settings

## Quick Start

### 1. Basic CAM Generation

Generate CAM for a single video:

```bash
python generate_cam.py \
    --checkpoint ./work_dir/baseline_res18_attention/_best_model.pt \
    --dataset phoenix2014 \
    --prefix ./dataset/phoenix2014/phoenix-2014-multisigner \
    --dict_path ./preprocess/phoenix2014/gloss_dict.npy \
    --select_id 0 \
    --output_dir ./CAM_results \
    --target_layers conv2d.corr2.conv_back conv2d.corr1.conv_back \
    --alpha 0.4 \
    --save_original
```

### 2. Batch Processing

Process multiple videos:

```bash
python batch_generate_cam.py \
    --checkpoint ./work_dir/baseline_res18_attention/_best_model.pt \
    --dataset phoenix2014 \
    --prefix ./dataset/phoenix2014/phoenix-2014-multisigner \
    --dict_path ./preprocess/phoenix2014/gloss_dict.npy \
    --start_id 0 \
    --end_id 10 \
    --output_dir ./batch_CAM_results \
    --target_layers conv2d.corr2.conv_back \
    --save_original
```

### 3. Advanced Configuration-Based Generation

Use the configuration file for complex experiments:

```bash
# Run all experiments defined in config
python advanced_cam_generator.py --config cam_config.yaml --create_comparisons

# Run specific experiment
python advanced_cam_generator.py \
    --config cam_config.yaml \
    --experiment correlation_only \
    --video_ids 0 1 2 3 4 \
    --create_comparisons
```

### 4. Create Comparison Visualizations

Generate comparison plots:

```bash
# Compare layers for each video
python visualize_cam_comparison.py \
    --input_dir ./CAM_results \
    --output_dir ./comparisons \
    --comparison_type layers \
    --max_frames 8

# Compare videos for specific layer
python visualize_cam_comparison.py \
    --input_dir ./CAM_results \
    --output_dir ./comparisons \
    --comparison_type videos \
    --layer_name conv2d_corr2_conv_back \
    --video_ids 0 1 2 3 4
```

## Target Layers

The model supports CAM generation for various layers:

### Correlation Attention Layers
- `conv2d.corr2.conv_back` - Main correlation attention (recommended)
- `conv2d.corr1.conv_back` - Earlier correlation attention
- `conv2d.corr3.conv_back` - Later correlation attention (if available)

### ResNet Backbone Layers
- `conv2d.layer4.1.conv2` - High-level features
- `conv2d.layer3.1.conv2` - Mid-level features
- `conv2d.layer2.1.conv2` - Low-level features

### Temporal Convolution Layers
- `conv1d.temporal_conv.branches.0.0` - Temporal features

## Output Structure

```
CAM_results/
├── video_0000/
│   ├── conv2d_corr2_conv_back/
│   │   └── video_name/
│   │       ├── frame_0000_cam.jpg      # CAM overlay
│   │       ├── frame_0000_heatmap.jpg  # Heatmap only
│   │       ├── frame_0000_original.jpg # Original frame
│   │       └── ...
│   └── conv2d_corr1_conv_back/
│       └── ...
├── video_0001/
└── ...
```

## Configuration File

The `cam_config.yaml` file allows you to:

- Define multiple experimental setups
- Set different layer combinations
- Configure visualization parameters
- Manage batch processing settings
- Control output formats

Key sections:
- `model`: Model checkpoint and architecture settings
- `dataset`: Data paths and preprocessing
- `cam`: CAM generation parameters
- `experiments`: Different layer combinations to test
- `visualization`: Comparison and plotting settings

## Tips and Best Practices

1. **Layer Selection**: Start with `conv2d.corr2.conv_back` as it captures the main attention mechanism

2. **Memory Management**: Process videos one at a time for large datasets to avoid memory issues

3. **Visualization**: Use alpha=0.4 for good overlay visibility, adjust based on your needs

4. **Comparison**: Generate layer comparisons to understand different attention patterns

5. **Batch Processing**: Use the batch script for processing many videos efficiently

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or process fewer videos at once
2. **Layer Not Found**: Check layer names using `model.named_modules()`
3. **No Gradients**: Ensure the model is in eval mode but gradients are enabled
4. **Empty CAM**: Check if the target layer produces meaningful activations

### Debug Mode

Enable debug mode in the configuration:

```yaml
debug:
  save_intermediate_features: true
  save_gradients: true
  verbose_hooks: true
```

## Requirements

- PyTorch >= 1.8.0
- OpenCV >= 4.5.0
- Matplotlib >= 3.4.0
- NumPy >= 1.20.0
- PyYAML >= 6.0
- tqdm >= 4.62.0

## Citation

If you use this CAM implementation in your research, please cite the original Grad-CAM paper:

```bibtex
@inproceedings{selvaraju2017grad,
  title={Grad-cam: Visual explanations from deep networks via gradient-based localization},
  author={Selvaraju, Ramprasaath R and Cogswell, Michael and Das, Abhishek and Vedantam, Ramakrishna and Parikh, Devi and Batra, Dhruv},
  booktitle={Proceedings of the IEEE international conference on computer vision},
  pages={618--626},
  year={2017}
}
```
